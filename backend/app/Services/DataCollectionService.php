<?php

namespace App\Services;

use App\Jobs\ProcessAlertChecking;
use App\Models\DataSource;
use App\Models\ProductData;
use App\Models\ProductSku;
use App\Models\ProductDataHistory;
use App\Models\ProductSkuHistory;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class DataCollectionService
{

    /**
     * 执行数据采集和标准化
     *
     * @param int $dataSourceId 数据源ID
     * @param mixed $itemId 目标商品ID
     * @param array $extraParams 额外参数
     * @param int|null $monitoringTaskId 监控任务ID（如果提供，将用于数据存储）
     * @return array
     */
    public function collectAndStandardize(int $dataSourceId, $itemId, array $extraParams = [], ?int $monitoringTaskId = null): array
    {
        Log::info('开始数据采集与标准化', [
            'data_source_id' => $dataSourceId,
            'item_id' => $itemId,
            'extra_params' => $extraParams,
            'monitoring_task_id' => $monitoringTaskId,
            'process_start_time' => now()->toDateTimeString()
        ]);

        try {
            // 1. 获取数据源配置
            Log::info('步骤1：开始获取数据源配置', ['data_source_id' => $dataSourceId]);
            $dataSource = DataSource::findOrFail($dataSourceId);
            Log::info('步骤1：数据源配置加载完成', [
                'data_source_id' => $dataSource->id,
                'data_source_name' => $dataSource->name,
                'api_method' => $dataSource->api_method,
                'api_url' => $dataSource->config['url'] ?? 'not_set',
                'timeout' => $dataSource->timeout,
                'config_full' => $dataSource->config,
                'field_mapping' => $dataSource->field_mapping,
                'default_params' => $dataSource->default_params
            ]);

            // 2. 构造API请求
            Log::info('步骤2：开始构造API请求', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'extra_params' => $extraParams
            ]);
            $requestData = $this->buildApiRequest($dataSource, $itemId, $extraParams);
            Log::info('步骤2：API请求构造完成', [
                'request_url' => $requestData['url'],
                'request_method' => $requestData['method'],
                'request_params' => $requestData['params'],
                'request_headers' => $requestData['headers'] ?? 'None',
                'request_timeout' => $requestData['timeout']
            ]);

            // 3. 发起HTTP请求
            Log::info('步骤3：开始发起HTTP请求');
            $response = $this->makeHttpRequest($requestData);
            Log::info('步骤3：HTTP请求完成', [
                'response_status' => $response->status(),
                'response_headers' => $response->headers(),
                'response_size' => strlen($response->body()) . ' bytes',
                'response_successful' => $response->successful()
            ]);

            // 4. 解析响应
            Log::info('步骤4：开始解析API响应');
            $rawData = $this->parseResponse($response, $dataSource);
            Log::info('步骤4：API响应解析完成', [
                'raw_data_keys' => array_keys($rawData),
                'raw_data_sample' => array_slice($rawData, 0, 3, true), // 只记录前3个字段作为样本
                'raw_data_full' => $rawData // 完整的原始数据
            ]);

            // 5. 标准化数据
            Log::info('步骤5：开始数据标准化', [
                'field_mapping_rules' => $dataSource->field_mapping,
                'raw_data_to_standardize' => $rawData
            ]);
            $standardizedData = $this->standardizeData($rawData, $dataSource->field_mapping);
            Log::info('步骤5：数据标准化完成', [
                'field_mapping_rules' => $dataSource->field_mapping,
                'standardized_product_fields_count' => count($standardizedData['product_fields']),
                'standardized_sku_count' => count($standardizedData['sku_fields']),
            ]);

            // 6. 存储数据
            Log::info('步骤6：开始存储数据到数据库');
            $productData = $this->storeData($dataSource, $itemId, $rawData, $standardizedData, $monitoringTaskId);
            Log::info('步骤6：数据存储完成', [
                'product_data_id' => $productData->id,
                'item_id' => $productData->item_id,
                'monitoring_task_id' => $productData->monitoring_task_id,
                'operation_type' => $productData->wasRecentlyCreated ? 'CREATE' : 'UPDATE',
                'created_at' => $productData->created_at,
                'updated_at' => $productData->updated_at,
                'last_collected_at' => $productData->last_collected_at,
                'raw_data' => $rawData,
                'standardized_data' => [
                    'product_fields' => $standardizedData['product_fields'],
                    'sku_fields' => $standardizedData['sku_fields'],
                ]
            ]);

            // 7. 触发警报检查
            Log::info('步骤7：开始触发警报检查');
            $this->dispatchAlertChecking($productData);
            Log::info('步骤7：警报检查任务已分发');

            Log::info('数据采集与标准化流程全部完成', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'monitoring_task_id' => $monitoringTaskId,
                'product_data_id' => $productData->id,
                'process_end_time' => now()->toDateTimeString(),
                'final_result' => [
                    'raw_data' => $rawData,
                    'standardized_data' => $standardizedData,
                    'database_record_id' => $productData->id
                ]
            ]);

            return [
                'success' => true,
                'product_data_id' => $productData->id,
                'raw_data' => $rawData,
                'standardized_data' => $standardizedData,
            ];

        } catch (\Exception $e) {
            Log::error('数据采集与标准化流程失败', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'monitoring_task_id' => $monitoringTaskId,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'process_end_time' => now()->toDateTimeString()
            ]);
            
            $this->handleException($e, $dataSourceId, $itemId);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 构造API请求
     */
    private function buildApiRequest(DataSource $dataSource, $itemId, array $extraParams): array
    {
        $config = $dataSource->config ?? [];
        Log::info('开始构造API请求', [
            'data_source_id' => $dataSource->id,
            'item_id' => $itemId,
            'config_keys' => array_keys($config),
            'auth_type' => $config['auth_type'] ?? 'none',
        ]);

        $url = $config['url'] ?? '';

        // 动态替换URL中的占位符
        $url = str_replace('{item_id}', $itemId, $url);

        // 合并基础参数 (来自config的默认参数、来自任务的额外参数)
        $params = array_merge($config['params'] ?? [], $dataSource->default_params ?? [], $extraParams);
        
        // 确保商品ID作为参数传递
        // 参数名可以在数据源配置中指定，默认为'item_id'
        $itemIdParamName = $config['item_id_param'] ?? 'item_id';
        if (!isset($params[$itemIdParamName]) && !empty($itemId)) {
            $params[$itemIdParamName] = $itemId;
        }

        // 处理认证配置
        $headers = $config['headers'] ?? [];
        $authType = $config['auth_type'] ?? 'none';
        
        Log::info('处理认证配置', [
            'auth_type' => $authType,
            'api_key_name' => $config['api_key_name'] ?? null,
            'has_api_key_value' => !empty($config['api_key_value']),
            'has_token' => !empty($config['token']),
        ]);

        switch ($authType) {
            case 'bearer':
                if (!empty($config['token'])) {
                    $headers['Authorization'] = 'Bearer ' . $config['token'];
                    Log::info('添加Bearer认证头');
                }
                break;
                
            case 'header_key':
                if (!empty($config['api_key_name']) && !empty($config['api_key_value'])) {
                    $headers[$config['api_key_name']] = $config['api_key_value'];
                    Log::info('添加API Key到请求头', [
                        'key_name' => $config['api_key_name']
                    ]);
                }
                break;
                
            case 'query_key':
                if (!empty($config['api_key_name']) && !empty($config['api_key_value'])) {
                    $params[$config['api_key_name']] = $config['api_key_value'];
                    Log::info('添加API Key到查询参数', [
                        'key_name' => $config['api_key_name']
                    ]);
                }
                break;
                
            case 'none':
            default:
                // 兼容旧版本：如果没有设置认证方式但config.params中有token，则使用它
                if (!empty($config['params']['token'])) {
                    Log::info('检测到旧版本token配置，使用config.params中的token');
                    // token已经在上面合并参数时包含了，这里只记录日志
                }
                
                // 同时检查config中的token并添加到查询参数
                if (!empty($config['token'])) {
                    $params['token'] = $config['token'];
                    Log::info('添加config中的token到查询参数', [
                        'token' => $config['token']
                    ]);
                }
                break;
        }
        
        // 构造请求数据
        $requestData = [
            'method' => strtoupper($config['method'] ?? 'GET'),
            'url' => $url,
            'params' => $params,
            'timeout' => $config['timeout'] ?? $dataSource->timeout ?? 30,
        ];
        
        // 只有当headers不为空时才添加
        if (!empty($headers)) {
            $requestData['headers'] = $headers;
        }
        
        Log::info('API请求构造完成', [
            'final_url' => $url,
            'final_params' => $params,
            'final_headers' => $headers,
            'auth_applied' => $authType !== 'none'
        ]);
        
        return $requestData;
    }

    /**
     * 发起HTTP请求
     */
    private function makeHttpRequest(array $requestData): \Illuminate\Http\Client\Response
    {
        // 构造完整的请求URL用于日志记录
        $fullUrl = $requestData['url'] . '?' . http_build_query($requestData['params']);
        Log::info('发起API请求', [
            'url' => $fullUrl,
            'method' => $requestData['method'],
            'headers' => $requestData['headers'] ?? 'None',
            'params' => $requestData['params'],
            'timeout' => $requestData['timeout'],
        ]);

        $response = Http::timeout($requestData['timeout']);
        
        // 只有当headers存在且不为空时才设置
        if (isset($requestData['headers']) && !empty($requestData['headers'])) {
            $response = $response->withHeaders($requestData['headers']);
        }
        
        // 解决SSL证书问题
        // 在生产环境中，应该配置php.ini指向正确的cacert.pem文件
        // 这里为了开发方便，暂时禁用SSL验证
        if (app()->environment('local')) {
            $response->withoutVerifying();
        }

        switch ($requestData['method']) {
            case 'POST':
                return $response->post($requestData['url'], $requestData['params']);
            case 'PUT':
                return $response->put($requestData['url'], $requestData['params']);
            case 'PATCH':
                return $response->patch($requestData['url'], $requestData['params']);
            case 'DELETE':
                return $response->delete($requestData['url'], $requestData['params']);
            default: // GET
                return $response->get($requestData['url'], $requestData['params']);
        }
    }

    /**
     * 解析HTTP响应
     */
    private function parseResponse(\Illuminate\Http\Client\Response $response, DataSource $dataSource): array
    {
        Log::info('收到API响应', [
            'status_code' => $response->status(),
            'headers' => $response->headers(),
            'body_preview' => mb_substr($response->body(), 0, 1000) . (mb_strlen($response->body()) > 1000 ? '...' : ''), // 记录前1000个字符
        ]);

        // 记录完整的响应体
        Log::info('API完整响应体', [
            'response_body' => $response->body(),
            'response_length' => strlen($response->body())
        ]);

        if (!$response->successful()) {
            throw new \Exception('API请求失败: HTTP ' . $response->status());
        }

        $responseBody = $response->body();
        Log::info('API响应体获取成功', [
            'response_length' => strlen($responseBody),
            'response_preview' => Str::limit($responseBody, 1000)
        ]);

        $data = $response->json();

        if (is_null($data)) {
            Log::error('JSON解析失败', [
                'response_body' => $responseBody,
                'json_last_error' => json_last_error_msg()
            ]);
            throw new \Exception("无法解析JSON响应: " . $responseBody);
        }

        Log::info('JSON解析成功', [
            'data_type' => gettype($data),
            'data_structure' => is_array($data) ? array_keys($data) : 'not_array',
            'data_count' => is_array($data) ? count($data) : 'not_countable',
            'parsed_data' => $data // 记录完整的解析后数据
        ]);

        // 检查API返回的code字段，如果是400表示商品下架
        if (isset($data['code']) && $data['code'] == 400) {
            Log::info('检测到商品下架状态', [
                'api_code' => $data['code'],
                'api_data' => $data['data'] ?? null,
                'left_nums' => $data['left_nums'] ?? null
            ]);
            
            // 返回一个特殊的下架状态数据结构
            return [
                'code' => 400,
                'state' => 0, // 下架状态
                'data' => $data['data'] ?? [],
                'left_nums' => $data['left_nums'] ?? null,
                'is_offline' => true // 标记为下架商品
            ];
        }

        return $data;
    }

    /**
     * 根据字段映射标准化数据
     *
     * @param array $rawData
     * @param array $mappingRules
     * @return array
     */
    private function standardizeData(array $rawData, ?array $mappingRules): array
    {
        if (empty($mappingRules) || empty($mappingRules['fields'])) {
            return ['product_fields' => [], 'sku_fields' => []];
        }

        $productFields = [];
        $skuFieldsList = [];
        $mappings = $mappingRules['fields'];
        
        // 定义应从原始数据根部提取的元数据字段
        $metadataFields = ['code'];

        // 检查是否为下架商品
        if (isset($rawData['is_offline']) && $rawData['is_offline'] === true) {
            Log::info('处理下架商品数据标准化', [
                'raw_data' => $rawData
            ]);
            
            // 对于下架商品，只设置基本的状态信息
            return [
                'product_fields' => [
                    'state' => 0, // 下架状态
                    'code' => $rawData['code'] ?? 400,
                ],
                'sku_fields' => []
            ];
        }

        // 首先，从原始数据中提取最外层的数据节点
        $dataNode = Arr::get($rawData, $mappingRules['data_path'] ?? null, $rawData);
        if (!is_array($dataNode)) {
            Log::warning('Data node for standardization is not an array or does not exist.', [
                'data_path' => $mappingRules['data_path'] ?? 'not_set',
                'raw_data' => $rawData,
            ]);
            $dataNode = []; // 避免后续错误
        }

        foreach ($mappings as $targetField => $source) {
            // 如果 source 是一个数组，它可能是skus集合的定义，直接跳过，因为它会被单独处理
            if (is_array($source)) {
                continue;
            }

            // 兼容数组下标写法：如 pic_urls[0] => pic_urls.0
            if (is_string($source) && preg_match('/(.+)\\[(\\d+)\\]$/', $source, $matches)) {
                $source = $matches[1] . '.' . $matches[2];
            }

            // 根据字段类型决定数据源：元数据从顶层取，其他从data_path节点取
            $dataSourceNode = in_array($targetField, $metadataFields) ? $rawData : $dataNode;

            // 提取数据，并增加驼峰式兼容
            $value = $this->extractValue($dataSourceNode, $source);
            if ($value === null) {
                $camelCaseSource = Str::camel($source);
                if ($camelCaseSource !== $source) {
                    $value = $this->extractValue($dataSourceNode, $camelCaseSource);
                }
            }

            // 处理简单的顶层产品字段
            if ($value !== null) {
                $transformation = $mappingRules['transformations'][$targetField] ?? null;
                $productFields[$targetField] = $this->transformValue($value, $transformation, $targetField);
            }
        }
        
        // 专门处理SKU集合
        if (isset($mappings['skus']) && is_array($mappings['skus'])) {
            $skuConfig = $mappings['skus'];
            $collectionSourcePath = $skuConfig['path'] ?? null;
            $collectionData = $collectionSourcePath ? Arr::get($dataNode, $collectionSourcePath) : [];
            $skuMappings = $skuConfig['fields'];

            if (is_array($collectionData)) {
                foreach ($collectionData as $item) {
                    $singleSkuFields = [];
                    foreach ($skuMappings as $skuTargetField => $skuSourcePath) {
                        $value = Arr::get($item, $skuSourcePath);
                        if ($value !== null) {
                             $transformation = $mappingRules['transformations']['skus'][$skuTargetField] ?? null;
                             $singleSkuFields[$skuTargetField] = $this->transformValue($value, $transformation, $skuTargetField);
                        }
                    }
                    if (!empty($singleSkuFields)) {
                        $skuFieldsList[] = $singleSkuFields;
                    }
                }
            }
        }
        
        // 采集商品状态逻辑调整 (基于已经提取的code)
        $code = $productFields['code'] ?? null;
        if ($code == 200) {
            $stateText = Arr::get($dataNode, 'state', null);
            if ($stateText === '上架') {
                $productFields['state'] = 1;
            } elseif ($stateText === '下架') {
                $productFields['state'] = 0;
            } // 其它情况不赋值，保持原有逻辑
        }
        
        return [
            'product_fields' => $productFields,
            'sku_fields' => $skuFieldsList,
        ];
    }

    /**
     * Extracts a value from raw data using a mapping rule, with transformation logic.
     * This is a simplified version for the refactoring.
     * A more complete implementation would be in the original `transformValue` method.
     */
    private function extractValue(array $rawData, $mapping)
    {
        if (is_string($mapping)) {
            return Arr::get($rawData, $mapping);
        }
        // Placeholder for more complex mapping with transformations
        return Arr::get($rawData, $mapping['source']);
    }

    /**
     * 转换单个值
     */
    private function transformValue($value, ?array $transformations, string $fieldType)
    {
        if (empty($transformations)) {
            return $value;
        }

        // 兼容旧的格式
        $type = $transformations['type'] ?? null;
        
        // 类型转换
        switch ($type) {
            case 'integer':
                // 先检查是否有映射规则
                if (isset($transformations['mapping']) && is_array($transformations['mapping'])) {
                    $key = (string)$value;
                    $value = $transformations['mapping'][$key] ?? (int) $value;
                } else {
                    $value = (int) $value;
                }
                break;
            case 'float':
                $value = (float) $value;
                break;
            case 'boolean':
                if (isset($transformations['mapping']) && is_array($transformations['mapping'])) {
                    $key = strtolower((string)$value);
                    $value = $transformations['mapping'][$key] ?? (filter_var($value, FILTER_VALIDATE_BOOLEAN));
                } else {
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                }
                break;
            case 'string':
                $value = (string) $value;
                break;
            case 'array':
                if (isset($transformations['ensure_array']) && $transformations['ensure_array'] && !is_array($value)) {
                    $value = $value === null ? [] : [$value];
                }
                break;
        }

        // 自定义转换规则
        if (isset($transformations['rules']) && is_array($transformations['rules'])) {
            foreach ($transformations['rules'] as $rule => $options) {
                switch ($rule) {
                    case 'min_from_array': // 例如: '最低到手价'
                        if (is_array($value)) {
                            $numericValues = array_filter($value, 'is_numeric');
                            $value = !empty($numericValues) ? min($numericValues) : null;
                        }
                        break;
                    case 'regex_match':
                        if (is_string($value) && isset($options['pattern'])) {
                            preg_match($options['pattern'], $value, $matches);
                            $value = $matches[$options['index'] ?? 0] ?? null;
                        }
                        break;
                    case 'string_replace':
                        if (is_string($value) && isset($options['search'], $options['replace'])) {
                            $value = str_replace($options['search'], $options['replace'], $value);
                        }
                        break;
                    case 'timestamp_to_date':
                        if (is_numeric($value)) {
                        $value = \Carbon\Carbon::createFromTimestamp($value)->format($options['format'] ?? 'Y-m-d H:i:s');
                        }
                        break;
                    case 'default':
                        if (empty($value) && isset($options['value'])) {
                            $value = $options['value'];
                        }
                        break;
                }
            }
        }
        
        // 兼容旧的默认值设置
        if (isset($transformations['default']) && empty($value)) {
             $value = $transformations['default'];
        }

        return $value;
    }

    /**
     * 存储数据到数据库
     */
    private function storeData(DataSource $dataSource, $itemId, array $rawData, array $standardizedData, ?int $monitoringTaskId): ProductData
    {
        $productFields = $standardizedData['product_fields'];
        $skuFields = $standardizedData['sku_fields'];
        $promotions = $standardizedData['product_fields']['promotion'] ?? [];

        Log::info('准备存储数据', [
            'promotions_from_standardized' => $promotions,
            'product_fields_keys' => array_keys($productFields),
            'sku_fields_count' => count($skuFields)
        ]);

        // Helper function to safely convert numeric fields
        $safeNumeric = function($value) {
            if ($value === '' || $value === null) {
                return null;
            }
            return is_numeric($value) ? $value : null;
        };

        // Prepare data for ProductData model
        $productUpdateData = [
            'monitoring_task_id' => $monitoringTaskId,
            'item_id' => $productFields['item_id'] ?? $itemId,
            'standardized_data' => $standardizedData, // Store both product and sku data for reference
            'last_collected_at' => now(),
            'code' => $safeNumeric($productFields['code'] ?? null), // 添加code字段，确保数字类型
            'title' => $productFields['title'] ?? null,
            'product_image' => $productFields['main_image_url'] ?? $productFields['product_image'] ?? null,
            'sales' => $safeNumeric($productFields['sales'] ?? null),
            'comment_count' => $safeNumeric($productFields['comment_count'] ?? null), // 修复空字符串问题
            'state' => $productFields['state'] ?? null, // 移除默认值，严格依赖API数据
            'has_sku' => !empty($skuFields),
            'item_type' => $productFields['item_type'] ?? null,
            'category_id' => $productFields['category_id'] ?? null,
            'category_path' => $productFields['category_path'] ?? null,
            'shop_id' => $safeNumeric($productFields['shop_id'] ?? null),
            'shop_name' => $productFields['shop_name'] ?? null,
            'props' => $productFields['props'] ?? null,
            'promotion' => $promotions, // 添加优惠信息
            'delivery_location' => $productFields['delivery_location'] ?? null,
        ];

        Log::info('准备更新的产品数据', [
            'promotions_in_update_data' => $productUpdateData['promotion'],
            'update_data_keys' => array_keys($productUpdateData)
        ]);

        // Calculate prices from SKUs
        if (!empty($skuFields)) {
            $subPrices = array_column($skuFields, 'promotion_price');
            $prices = array_column($skuFields, 'price');
            
            // 过滤掉非数值和null值
            $validSubPrices = array_filter($subPrices, 'is_numeric');
            $validPrices = array_filter($prices, 'is_numeric');

            $productUpdateData['price'] = !empty($validPrices) ? min($validPrices) : $safeNumeric($productFields['price'] ?? null);
            $productUpdateData['lowest_price'] = !empty($validSubPrices) ? min($validSubPrices) : $productUpdateData['price'];
            $productUpdateData['highest_price'] = !empty($validPrices) ? max($validPrices) : $productUpdateData['price'];

        } else {
            $productUpdateData['price'] = $safeNumeric($productFields['price'] ?? null);
            $productUpdateData['lowest_price'] = $safeNumeric($productFields['lowest_price'] ?? null) ?? $productUpdateData['price'];
            $productUpdateData['highest_price'] = $safeNumeric($productFields['highest_price'] ?? null) ?? $productUpdateData['price'];
        }

        $productData = ProductData::updateOrCreate(
            [
                'monitoring_task_id' => $monitoringTaskId,
                'item_id' => $productFields['item_id'] ?? $itemId,
            ],
            $productUpdateData
        );

        Log::info('产品数据已保存', [
            'product_data_id' => $productData->id,
            'saved_promotion' => $productData->promotion,
            'operation_type' => $productData->wasRecentlyCreated ? 'CREATE' : 'UPDATE'
        ]);

        // Sync SKUs
        if ($productData->id) {
            // Delete old SKUs first
            $productData->skus()->delete();
            
            if (!empty($skuFields)) {
                $skuDataToInsert = array_map(function ($sku) use ($productData, $safeNumeric) {
                    return [
                        'product_data_id' => $productData->id,
                        'sku_id' => $sku['sku_id'] ?? null,
                        'name' => $sku['name'] ?? $sku['sku_name'] ?? 'SKU-' . ($sku['sku_id'] ?? 'unknown'),
                        'price' => $safeNumeric($sku['price'] ?? 0),
                        'sub_price' => $safeNumeric($sku['sub_price'] ?? $sku['promotion_price'] ?? null),
                        'sub_price_title' => $sku['sub_price_title'] ?? null,
                        'quantity' => $safeNumeric($sku['stock'] ?? 0),
                        'image_url' => $sku['image_url'] ?? null,
                        'official_guide_price' => $safeNumeric($sku['official_guide_price'] ?? null),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }, $skuFields);
                ProductSku::insert($skuDataToInsert);
            }
        }

        // 更新产品总库存
        $totalStock = $productData->skus()->sum('quantity');
        $productData->stock = $totalStock;
        $productData->save();

        // 保存到历史表
        $this->saveToHistory($productData, $skuFields, $safeNumeric);
        
        return $productData;
    }

    /**
     * 保存数据到历史表
     *
     * @param ProductData $productData
     * @param array $skuFields
     * @param callable $safeNumeric
     * @return void
     */
    private function saveToHistory(ProductData $productData, array $skuFields, callable $safeNumeric): void
    {
        try {
            Log::info('开始保存历史数据', [
                'product_data_id' => $productData->id,
                'item_id' => $productData->item_id,
                'monitoring_task_id' => $productData->monitoring_task_id
            ]);

            // 保存商品历史数据
            $historyData = [
                'monitoring_task_id' => $productData->monitoring_task_id,
                'item_id' => $productData->item_id,
                'standardized_data' => $productData->standardized_data,
                'collected_at' => $productData->last_collected_at,
                'code' => $productData->code,
                'title' => $productData->title,
                'product_image' => $productData->product_image,
                'price' => $productData->price,
                'lowest_price' => $productData->lowest_price,
                'highest_price' => $productData->highest_price,
                'min_hand_price' => $productData->min_hand_price,
                'max_hand_price' => $productData->max_hand_price,
                'stock' => $productData->stock,
                'sales' => $productData->sales,
                'comment_count' => $productData->comment_count,
                'state' => $productData->state,
                'has_sku' => $productData->has_sku,
                'item_type' => $productData->item_type,
                'category_id' => $productData->category_id,
                'category_path' => $productData->category_path,
                'shop_id' => $productData->shop_id,
                'shop_name' => $productData->shop_name,
                'props' => $productData->props,
                'promotion' => $productData->promotion,
                'delivery_location' => $productData->delivery_location,
                'product_url' => $productData->product_url,
            ];

            $productDataHistory = ProductDataHistory::create($historyData);

            Log::info('商品历史数据已保存', [
                'product_data_history_id' => $productDataHistory->id,
                'product_data_id' => $productData->id
            ]);

            // 保存SKU历史数据
            if (!empty($skuFields)) {
                $skuHistoryData = array_map(function ($sku) use ($productDataHistory, $safeNumeric) {
                    // Re-implement deviation rate calculation logic here for historical accuracy
                    $price = $safeNumeric($sku['price'] ?? 0);
                    $subPrice = $safeNumeric($sku['sub_price'] ?? $sku['promotion_price'] ?? null);
                    $officialGuidePrice = $safeNumeric($sku['official_guide_price'] ?? null);

                    $promoRate = null;
                    if ($price > 0) {
                        $promoSubPrice = $subPrice ?? $price;
                        $promoRate = round((($price - $promoSubPrice) / $price) * 100, 2);
                    }

                    $channelRate = null;
                    if ($officialGuidePrice > 0) {
                        $channelSubPrice = $subPrice ?? $price;
                        if (!$channelSubPrice || $channelSubPrice <= 0) {
                            $channelSubPrice = $price ?? 0;
                        }
                        if ($channelSubPrice > 0) {
                            $channelRate = round((($officialGuidePrice - $channelSubPrice) / $officialGuidePrice) * 100, 2);
                        }
                    }

                    return [
                        'product_data_history_id' => $productDataHistory->id,
                        'sku_id' => $sku['sku_id'] ?? null,
                        'name' => $sku['name'] ?? $sku['sku_name'] ?? 'SKU-' . ($sku['sku_id'] ?? 'unknown'),
                        'price' => $price,
                        'sub_price' => $subPrice,
                        'sub_price_title' => $sku['sub_price_title'] ?? null,
                        'quantity' => $safeNumeric($sku['stock'] ?? 0),
                        'image_url' => $sku['image_url'] ?? null,
                        'official_guide_price' => $officialGuidePrice,
                        'promotion_deviation_rate' => $promoRate,
                        'channel_price_deviation_rate' => $channelRate,
                        'collected_at' => $productDataHistory->collected_at,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }, $skuFields);

                ProductSkuHistory::insert($skuHistoryData);

                Log::info('SKU历史数据已保存', [
                    'product_data_history_id' => $productDataHistory->id,
                    'sku_count' => count($skuHistoryData)
                ]);
            }

        } catch (\Exception $e) {
            Log::error('保存历史数据失败', [
                'product_data_id' => $productData->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 分发预警检查任务到队列
     *
     * @param ProductData $productData
     * @return void
     */
    private function dispatchAlertChecking(ProductData $productData): void
    {
        try {
            ProcessAlertChecking::dispatch($productData->id)
                ->onQueue('alerts') // 使用专门的队列处理预警
                ->delay(now()->addSeconds(5)); // 延迟5秒执行，确保数据已完全保存

            Log::debug('预警检查任务已分发到队列', [
                'product_data_id' => $productData->id,
            ]);
        } catch (\Exception $e) {
            Log::error('分发预警检查任务失败', [
                'product_data_id' => $productData->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    private function handleException(\Exception $e, int $dataSourceId, string $itemId = null): void
    {
        $context = [
            'data_source_id' => $dataSourceId,
        ];
        if ($itemId) {
            $context['item_id'] = $itemId;
        }

        // 包含更详细的错误信息
        $context['error'] = $e->getMessage();
        $context['trace'] = mb_substr($e->getTraceAsString(), 0, 2000); // 记录部分堆栈跟踪

        Log::error('数据采集与标准化失败', $context);
    }
} 