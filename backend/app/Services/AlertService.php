<?php

namespace App\Services;

use App\Jobs\SendAlertNotification;
use App\Models\Alert;
use App\Models\AlertRule;
use App\Models\ProductData;
use App\Models\MonitoringTask;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;

class AlertService
{
    /**
     * 检查并处理产品数据的预警条件
     *
     * @param ProductData $productData
     * @return array
     */
    public function processAlerts(ProductData $productData): array
    {
        Log::info('开始处理产品数据预警', [
            'product_data_id' => $productData->id,
            'item_id' => $productData->item_id,
            'monitoring_task_id' => $productData->monitoring_task_id,
        ]);

        $triggeredAlerts = [];

        try {
            // 获取相关的监控任务，并预加载其所有激活的预警规则
            $monitoringTask = MonitoringTask::with(['alertRules' => function ($query) {
                    $query->where('status', 1); // 1 表示启用
                }])
                ->find($productData->monitoring_task_id);

            if (!$monitoringTask || $monitoringTask->status !== 'active') {
                 Log::info('未找到激活的监控任务或任务非激活状态，跳过处理。', ['monitoring_task_id' => $productData->monitoring_task_id]);
                 return ['success' => true, 'triggered_alerts' => [], 'alerts_count' => 0];
            }
            
            // 检查任务是否包含当前产品 (如果 target_products 不为空)
            if (!empty($monitoringTask->target_products) && !in_array($productData->item_id, $monitoringTask->target_products)) {
                 Log::info('当前产品不在此任务的监控目标中，跳过处理。', [
                    'monitoring_task_id' => $monitoringTask->id,
                    'item_id' => $productData->item_id,
                 ]);
                 return ['success' => true, 'triggered_alerts' => [], 'alerts_count' => 0];
            }
            
            foreach ($monitoringTask->alertRules as $rule) {
                $alert = $this->checkAlertRule($rule, $productData, $monitoringTask);
                if ($alert) {
                    $triggeredAlerts[] = $alert;
                }
            }


            Log::info('产品数据预警处理完成', [
                'product_data_id' => $productData->id,
                'triggered_alerts_count' => count($triggeredAlerts),
            ]);

            return [
                'success' => true,
                'triggered_alerts' => $triggeredAlerts,
                'alerts_count' => count($triggeredAlerts),
            ];

        } catch (\Exception $e) {
            Log::error('处理产品数据预警失败', [
                'product_data_id' => $productData->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 检查单个预警规则
     *
     * @param AlertRule $rule
     * @param ProductData $productData
     * @param MonitoringTask $task
     * @return Alert|null
     */
    private function checkAlertRule(AlertRule $rule, ProductData $productData, MonitoringTask $task): ?Alert
    {
        // 检查规则是否在冷却期内
        if ($rule->isInCooldown()) {
            Log::debug('预警规则在冷却期内，跳过', ['rule_id' => $rule->id]);
            return null;
        }

        // 检查是否达到频率限制
        if ($rule->hasReachedHourlyLimit() || $rule->hasReachedDailyLimit()) {
            Log::debug('预警规则达到频率限制，跳过', ['rule_id' => $rule->id]);
            return null;
        }

        // 获取目标字段值
        $targetValue = $this->getTargetFieldValue($productData, $rule->target_field);
        if ($targetValue === null) {
            Log::debug('目标字段值为空，跳过预警检查', [
                'rule_id' => $rule->id,
                'target_field' => $rule->target_field,
            ]);
            return null;
        }

        // 检查预警条件
        $isTriggered = $this->evaluateCondition($targetValue, $rule);

        if (!$isTriggered) {
            return null;
        }

        // 创建预警记录
        $alert = $this->createAlert($rule, $productData, $task, $targetValue);

        // 更新规则触发统计
        $this->updateRuleStatistics($rule);

        // 异步发送通知
        $this->dispatchNotification($alert);

        Log::info('预警规则触发', [
            'rule_id' => $rule->id,
            'rule_name' => $rule->name,
            'alert_id' => $alert->id,
            'target_value' => $targetValue,
        ]);

        return $alert;
    }

    /**
     * 获取目标字段的值
     *
     * @param ProductData $productData
     * @param string $targetField
     * @return mixed
     */
    private function getTargetFieldValue(ProductData $productData, string $targetField)
    {
        $standardizedData = $productData->standardized_data ?? [];
        
        // 支持嵌套字段访问，如 'price.current' 或 'stock.quantity'
        return Arr::get($standardizedData, $targetField);
    }

    /**
     * 评估预警条件
     *
     * @param mixed $targetValue
     * @param AlertRule $rule
     * @return bool
     */
    private function evaluateCondition($targetValue, AlertRule $rule): bool
    {
        $operator = $rule->operator;
        $thresholdValues = $rule->threshold_values ?? [];

        switch ($operator) {
            case '>':
                return $this->compareNumeric($targetValue, '>', $thresholdValues[0] ?? 0);
            
            case '<':
                return $this->compareNumeric($targetValue, '<', $thresholdValues[0] ?? 0);
            
            case '>=':
                return $this->compareNumeric($targetValue, '>=', $thresholdValues[0] ?? 0);
            
            case '<=':
                return $this->compareNumeric($targetValue, '<=', $thresholdValues[0] ?? 0);
            
            case '=':
            case '==':
                return $targetValue == ($thresholdValues[0] ?? null);
            
            case '!=':
                return $targetValue != ($thresholdValues[0] ?? null);
            
            case 'between':
                return $this->isBetween($targetValue, $thresholdValues[0] ?? 0, $thresholdValues[1] ?? 0);
            
            case 'not_between':
                return !$this->isBetween($targetValue, $thresholdValues[0] ?? 0, $thresholdValues[1] ?? 0);
            
            case 'contains':
                return str_contains((string)$targetValue, (string)($thresholdValues[0] ?? ''));
            
            case 'not_contains':
                return !str_contains((string)$targetValue, (string)($thresholdValues[0] ?? ''));
            
            case 'starts_with':
                return str_starts_with((string)$targetValue, (string)($thresholdValues[0] ?? ''));
            
            case 'ends_with':
                return str_ends_with((string)$targetValue, (string)($thresholdValues[0] ?? ''));
            
            case 'regex':
                return preg_match('/' . ($thresholdValues[0] ?? '') . '/', (string)$targetValue);
            
            case 'change_rate':
                return $this->checkChangeRate($targetValue, $rule, $thresholdValues);
            
            case 'percentage_change':
                return $this->checkPercentageChange($targetValue, $rule, $thresholdValues);
            
            default:
                Log::warning('未知的预警操作符', ['operator' => $operator, 'rule_id' => $rule->id]);
                return false;
        }
    }

    /**
     * 数值比较
     */
    private function compareNumeric($value, string $operator, $threshold): bool
    {
        if (!is_numeric($value) || !is_numeric($threshold)) {
            return false;
        }

        $value = (float)$value;
        $threshold = (float)$threshold;

        switch ($operator) {
            case '>':
                return $value > $threshold;
            case '<':
                return $value < $threshold;
            case '>=':
                return $value >= $threshold;
            case '<=':
                return $value <= $threshold;
            default:
                return false;
        }
    }

    /**
     * 检查值是否在范围内
     */
    private function isBetween($value, $min, $max): bool
    {
        if (!is_numeric($value) || !is_numeric($min) || !is_numeric($max)) {
            return false;
        }

        $value = (float)$value;
        $min = (float)$min;
        $max = (float)$max;

        return $value >= $min && $value <= $max;
    }

    /**
     * 检查变化率
     */
    private function checkChangeRate($currentValue, AlertRule $rule, array $thresholdValues): bool
    {
        // 获取历史数据进行比较
        $previousData = ProductData::where('monitoring_task_id', $rule->monitoring_task_id)
            ->where('item_id', '!=', null)
            ->where('created_at', '<', now()->subHours(1)) // 1小时前的数据
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$previousData) {
            return false;
        }

        $previousValue = $this->getTargetFieldValue($previousData, $rule->target_field);
        if (!is_numeric($currentValue) || !is_numeric($previousValue) || $previousValue == 0) {
            return false;
        }

        $changeRate = ($currentValue - $previousValue) / $previousValue;
        $threshold = ($thresholdValues[0] ?? 0) / 100; // 转换为小数

        return abs($changeRate) >= abs($threshold);
    }

    /**
     * 检查百分比变化
     */
    private function checkPercentageChange($currentValue, AlertRule $rule, array $thresholdValues): bool
    {
        // 类似于变化率检查，但使用百分比阈值
        return $this->checkChangeRate($currentValue, $rule, $thresholdValues);
    }

    /**
     * 创建预警记录
     */
    private function createAlert(AlertRule $rule, ProductData $productData, MonitoringTask $task, $targetValue): Alert
    {
        $message = $this->generateAlertMessage($rule, $productData, $targetValue);

        return Alert::create([
            'monitoring_task_id' => $task->id,
            'alert_rule_id' => $rule->id,
            'product_id' => $productData->id,
            'alert_type' => $rule->rule_type,
            'alert_level' => $rule->severity,
            'title' => $rule->name,
            'message' => $message,
            'trigger_data' => [
                'target_field' => $rule->target_field,
                'target_value' => $targetValue,
                'operator' => $rule->operator,
                'threshold_values' => $rule->threshold_values,
                'product_data' => [
                    'item_id' => $productData->item_id,
                    'data_source' => $productData->monitoringTask?->dataSource?->name ?? '未知',
                    'standardized_data' => $productData->standardized_data,
                ],
            ],
            'status' => 0, // 未处理
        ]);
    }

    /**
     * 生成预警消息
     */
    private function generateAlertMessage(AlertRule $rule, ProductData $productData, $targetValue): string
    {
        $standardizedData = $productData->standardized_data ?? [];
        $productTitle = $standardizedData['title'] ?? $standardizedData['name'] ?? "商品ID: {$productData->item_id}";
        
        $thresholdText = '';
        if (!empty($rule->threshold_values)) {
            if (count($rule->threshold_values) === 1) {
                $thresholdText = $rule->threshold_values[0];
            } else {
                $thresholdText = implode(' - ', $rule->threshold_values);
            }
        }

        return sprintf(
            '产品 "%s" 的 %s 当前值为 %s，触发了预警条件 %s %s',
            $productTitle,
            $rule->target_field,
            $targetValue,
            $rule->operator,
            $thresholdText
        );
    }

    /**
     * 更新规则触发统计
     */
    private function updateRuleStatistics(AlertRule $rule): void
    {
        $rule->increment('trigger_count');
        $rule->update([
            'last_triggered_at' => now(),
        ]);
    }

    /**
     * 分发通知发送任务到队列
     *
     * @param Alert $alert
     * @return void
     */
    private function dispatchNotification(Alert $alert): void
    {
        try {
            // 检查是否配置了通知渠道
            $notificationChannels = $alert->alertRule->notification_channels ?? [];
            
            if (empty($notificationChannels)) {
                Log::debug('预警规则未配置通知渠道，跳过通知发送', [
                    'alert_id' => $alert->id,
                    'alert_rule_id' => $alert->alert_rule_id,
                ]);
                return;
            }

            SendAlertNotification::dispatch($alert->id)
                ->onQueue('notifications') // 使用专门的队列处理通知
                ->delay(now()->addSeconds(2)); // 延迟2秒执行，确保预警记录已完全保存

            Log::debug('预警通知任务已分发到队列', [
                'alert_id' => $alert->id,
                'notification_channels' => $notificationChannels,
            ]);
        } catch (\Exception $e) {
            Log::error('分发预警通知任务失败', [
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
} 