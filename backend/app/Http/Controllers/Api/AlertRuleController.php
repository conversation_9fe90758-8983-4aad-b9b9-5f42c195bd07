<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AlertRule;
use App\Models\Alert;
use Carbon\Carbon;
use App\Models\MonitoringTask;
use App\Models\TaskGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\ValidationException;

class AlertRuleController extends Controller
{
    /**
     * 获取预警规则列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = AlertRule::with(['user'])
                ->where('user_id', Auth::id());

            // 筛选条件
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('rule_type')) {
                $query->where('rule_type', $request->rule_type);
            }

            if ($request->has('severity')) {
                $query->where('severity', $request->severity);
            }

            // 搜索
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // 排序
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // 分页
            $perPage = $request->get('per_page', 15);
            $rules = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $rules,
                'message' => '预警规则列表获取成功'
            ]);

        } catch (\Exception $e) {
            \Log::error('获取预警规则列表失败: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'data' => [
                    'data' => [],
                    'total' => 0,
                    'per_page' => $request->get('per_page', 15),
                    'current_page' => 1,
                    'last_page' => 1
                ],
                'message' => '获取预警规则列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建预警规则
     */
    public function store(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'rule_type' => 'required|array',
            'rule_type.*' => ['string', Rule::in(array_keys(AlertRule::getAvailableRuleTypes()))],
            'conditions' => 'nullable|array',
            'severity' => ['required', 'string', Rule::in(array_keys(AlertRule::getAvailableSeverities()))],
            'priority' => ['required', 'string', Rule::in(array_keys(AlertRule::getAvailablePriorities()))],
            'notification_method' => 'required|array',
            'notification_method.*' => ['required', 'string', Rule::in(array_keys(AlertRule::getAvailableNotificationMethods()))],
            'status' => 'boolean'
        ]);

        $ruleTypes = $request->input('rule_type');
        $conditions = $request->input('conditions', []);

        foreach ($ruleTypes as $ruleType) {
            if ($this->ruleTypeNeedsThreshold($ruleType)) {
                if (
                    !isset($conditions[$ruleType]) ||
                    !isset($conditions[$ruleType]['operator']) ||
                    !isset($conditions[$ruleType]['threshold'])
                ) {
                    return response()->json([
                        'message' => 'Validation Error',
                        'errors' => ["conditions.{$ruleType}" => ["当规则类型为 {$ruleType} 时，操作符和阈值是必需的。"]]
                    ], 422);
                }
            }
        }
        
        $validatedData['user_id'] = Auth::id();

        $alertRule = AlertRule::create($validatedData);

        return response()->json([
            'success' => true,
            'message' => '预警规则创建成功',
            'data' => $alertRule
        ], 201);
    }

    /**
     * 获取单个预警规则详情
     */
    public function show(AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权访问此预警规则'
            ], 403);
        }

        $alertRule->load(['user']);

        return response()->json([
            'success' => true,
            'data' => $alertRule,
            'message' => '预警规则详情获取成功'
        ]);
    }

    /**
     * 更新预警规则
     */
    public function update(Request $request, AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权修改此预警规则'
            ], 403);
        }

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'rule_type' => 'required|array',
            'rule_type.*' => ['string', Rule::in(array_keys(AlertRule::getAvailableRuleTypes()))],
            'conditions' => 'nullable|array',
            'severity' => ['required', 'string', Rule::in(array_keys(AlertRule::getAvailableSeverities()))],
            'priority' => ['required', 'string', Rule::in(array_keys(AlertRule::getAvailablePriorities()))],
            'notification_method' => 'required|array',
            'notification_method.*' => ['required', 'string', Rule::in(array_keys(AlertRule::getAvailableNotificationMethods()))],
            'status' => 'boolean'
        ]);
        
        $ruleTypes = $request->input('rule_type');
        $conditions = $request->input('conditions', []);

        foreach ($ruleTypes as $ruleType) {
            if ($this->ruleTypeNeedsThreshold($ruleType)) {
                if (
                    !isset($conditions[$ruleType]) ||
                    !isset($conditions[$ruleType]['operator']) ||
                    !isset($conditions[$ruleType]['threshold'])
                ) {
                    return response()->json([
                        'message' => 'Validation Error',
                        'errors' => ["conditions.{$ruleType}" => ["当规则类型为 {$ruleType} 时，操作符和阈值是必需的。"]]
                    ], 422);
                }
            }
        }
        
        // 清理不再使用的 conditions
        $activeConditions = [];
        foreach ($ruleTypes as $type) {
            if (isset($conditions[$type])) {
                $activeConditions[$type] = $conditions[$type];
            }
        }
        $validatedData['conditions'] = $activeConditions;
        
        $alertRule->update($validatedData);

        return response()->json([
            'success' => true,
            'message' => '预警规则更新成功',
            'data' => $alertRule
        ]);
    }

    /**
     * 检查规则类型是否需要阈值
     */
    private function ruleTypeNeedsThreshold(string $ruleType): bool
    {
        $typesWithThreshold = ['promotion_price_deviation', 'channel_price_deviation'];
        return in_array($ruleType, $typesWithThreshold);
    }

    /**
     * 更新预警规则状态
     */
    public function updateStatus(Request $request, AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权修改此预警规则'
            ], 403);
        }

        $validatedData = $request->validate([
            'status' => 'required|boolean'
        ]);

        $alertRule->update(['status' => $validatedData['status']]);

        return response()->json([
            'success' => true,
            'data' => $alertRule,
            'message' => '预警规则状态更新成功'
        ]);
    }

    /**
     * 删除预警规则
     */
    public function destroy(AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权删除此预警规则'
            ], 403);
        }

        $alertRule->delete();

        return response()->json([
            'success' => true,
            'message' => '预警规则删除成功'
        ]);
    }

    /**
     * 批量删除预警规则
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:alert_rules,id'
        ]);

        $deletedCount = AlertRule::whereIn('id', $validated['ids'])
            ->where('user_id', Auth::id())
            ->delete();

        return response()->json([
            'success' => true,
            'data' => ['deleted_count' => $deletedCount],
            'message' => "成功删除 {$deletedCount} 个预警规则"
        ]);
    }

    /**
     * 启用预警规则
     */
    public function activate(AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权操作此预警规则'
            ], 403);
        }

        $alertRule->update(['status' => 1]);

        return response()->json([
            'success' => true,
            'data' => $alertRule,
            'message' => '预警规则已启用'
        ]);
    }

    /**
     * 禁用预警规则
     */
    public function deactivate(AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权操作此预警规则'
            ], 403);
        }

        $alertRule->update(['status' => 0]);

        return response()->json([
            'success' => true,
            'data' => $alertRule,
            'message' => '预警规则已禁用'
        ]);
    }

    /**
     * 批量更新预警规则状态
     */
    public function batchUpdateStatus(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:alert_rules,id',
            'status' => 'required|in:0,1'
        ]);

        $updatedCount = AlertRule::whereIn('id', $validated['ids'])
            ->where('user_id', Auth::id())
            ->update(['status' => $validated['status']]);

        $statusText = $validated['status'] === 1 ? '启用' : '禁用';

        return response()->json([
            'success' => true,
            'data' => ['updated_count' => $updatedCount],
            'message' => "成功{$statusText} {$updatedCount} 个预警规则"
        ]);
    }

    /**
     * 获取预警规则统计信息
     */
    public function statistics(): JsonResponse
    {
        $userId = Auth::id();

        $stats = [
            'total_rules' => AlertRule::where('user_id', $userId)->count(),
            'active_rules' => AlertRule::where('user_id', $userId)->where('status', 1)->count(),
            'inactive_rules' => AlertRule::where('user_id', $userId)->where('status', 0)->count(),
            'rules_by_type' => AlertRule::where('user_id', $userId)
                ->selectRaw('rule_type, COUNT(*) as count')
                ->groupBy('rule_type')
                ->pluck('count', 'rule_type'),
            'rules_by_severity' => AlertRule::where('user_id', $userId)
                ->selectRaw('severity, COUNT(*) as count')
                ->groupBy('severity')
                ->pluck('count', 'severity'),
            'recent_alerts' => AlertRule::where('user_id', $userId)
                ->whereHas('alerts', function ($query) {
                    $query->where('created_at', '>=', now()->subDays(7));
                })
                ->count()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => '预警规则统计信息获取成功'
        ]);
    }

    /**
     * 获取可用选项
     */
    public function options(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'rule_types' => AlertRule::getAvailableRuleTypes(),
                'operators' => AlertRule::getAvailableOperators(),
                'severities' => AlertRule::getAvailableSeverities(),
                'priorities' => AlertRule::getAvailablePriorities(),
                'notification_methods' => AlertRule::getAvailableNotificationMethods(),
            ],
            'message' => '可用选项获取成功'
        ]);
    }

    /**
     * 获取预警规则的统计数据
     */
    public function stats(): JsonResponse
    {
        try {
            $userId = Auth::id();

            $activeRules = AlertRule::where('user_id', $userId)->where('status', true)->count();

            // 检查Alert表是否存在数据，并处理可能的字段不存在问题
            $todayAlerts = 0;
            $unreadAlerts = 0;
            $totalAlerts = 0;

            try {
                // 检查Alert表是否有is_read字段
                if (Schema::hasColumn('alerts', 'is_read')) {
                    $unreadAlerts = Alert::whereHas('alertRule', function ($query) use ($userId) {
                        $query->where('user_id', $userId);
                    })->where('is_read', false)->count();
                }

                $todayAlerts = Alert::whereHas('alertRule', function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                })->whereDate('created_at', Carbon::today())->count();

                $totalAlerts = Alert::whereHas('alertRule', function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                })->count();

            } catch (\Exception $e) {
                // 如果Alert表查询失败，记录错误但不影响其他统计
                \Log::warning('Alert统计查询失败: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'activeRules' => $activeRules,
                    'todayAlerts' => $todayAlerts,
                    'unreadAlerts' => $unreadAlerts,
                    'totalAlerts' => $totalAlerts,
                ],
                'message' => '统计数据获取成功'
            ]);

        } catch (\Exception $e) {
            \Log::error('获取预警规则统计数据失败: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'data' => [
                    'activeRules' => 0,
                    'todayAlerts' => 0,
                    'unreadAlerts' => 0,
                    'totalAlerts' => 0,
                ],
                'message' => '统计数据获取失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 