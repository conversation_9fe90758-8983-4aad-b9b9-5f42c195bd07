import axios from 'axios';
import { useAuthStore } from '../stores/auth.ts';
import { ElMessage } from 'element-plus';

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore();
    const token = authStore.token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 处理204 No Content和304 Not Modified状态码
    if (response.status === 204 || response.status === 304) {
      // 保留完整的response对象，让调用方能够访问status
      return response;
    }

    // 对于其他状态码，保持原有行为但也保留status信息
    return {
      ...response,
      data: response.data
    };
  },
  (error) => {
    const authStore = useAuthStore();

    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 204:
          // 204 No Content - 不显示错误消息，让调用方处理
          return Promise.reject(error);
        case 304:
          // 304 Not Modified - 不显示错误消息，让调用方处理
          return Promise.reject(error);
        case 401:
          // 未授权，可能是token过期
          authStore.logout();
          window.location.href = '/login';
          ElMessage.error('登录已过期，请重新登录');
          break;
        case 403:
          // 禁止访问
          ElMessage.error(data.message || '您没有权限执行此操作');
          break;
        case 404:
          ElMessage.error('请求的资源未找到');
          break;
        case 422:
          // 表单验证错误
          const errors = Object.values(data.errors).flat();
          ElMessage.error(errors.join('\n') || '表单验证失败');
          break;
        case 500:
        case 501:
        case 502:
        case 503:
          ElMessage.error(data.message || '服务器错误，请稍后再试');
          break;
        default:
          ElMessage.error(data.message || `请求错误: ${status}`);
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      ElMessage.error('无法连接到服务器，请检查您的网络连接');
    } else {
      // 发送请求时出了点问题
      ElMessage.error(`请求失败: ${error.message}`);
    }

    return Promise.reject(error);
  }
);

export default apiClient; 