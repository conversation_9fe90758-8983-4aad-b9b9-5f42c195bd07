<template>
  <div class="channel-price-dashboard">
    <!-- 页面标题和统计信息 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">渠道价格监测看板</h1>
        <p class="page-description">监控各渠道的产品价格变化，进行跨平台价格对比分析</p>
      </div>
      <div class="header-stats">
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ dashboardStats.taskGroups || 0 }}</div>
          <div class="stat-label">任务组</div>
        </div>
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ dashboardStats.monitoringProducts || 0 }}</div>
          <div class="stat-label">监控产品</div>
        </div>
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ dashboardStats.runningTasks || 0 }}</div>
          <div class="stat-label">运行中任务</div>
        </div>
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ dashboardStats.alerts || 0 }}</div>
          <div class="stat-label">预警总数</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索栏 -->
    <div class="filter-bar">
      <el-row :gutter="16">
        <el-col :span="5">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索产品名称或ID"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.task_id"
            placeholder="选择监控任务"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="task in monitoringTasks"
              :key="task.id"
              :label="task.name"
              :value="task.id"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select
            v-model="searchForm.data_source_id"
            placeholder="选择数据源"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="source in dataSources"
              :key="source.id"
              :label="source.name"
              :value="source.id"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select
            v-model="timeMode"
            placeholder="时间模式"
            @change="handleTimeModeChange"
          >
            <el-option label="查看最新数据" value="latest" />
            <el-option label="指定时间点" value="specific" />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-date-picker
            v-if="timeMode === 'specific'"
            v-model="specificTime"
            type="datetime"
            placeholder="选择查看时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleSpecificTimeChange"
          />
          <el-date-picker
            v-else
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleDateRangeChange"
          />
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters" :icon="RefreshLeft">重置</el-button>
          <el-button 
            type="primary" 
            @click="showCompareDialog" 
            :disabled="selectedProducts.length < 2"
            :icon="TrendCharts"
          >
            对比 ({{ selectedProducts.length }})
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 产品数据表格 -->
    <div class="data-table">
      <el-table
        ref="productTable"
        v-loading="loading"
        :data="productData"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        style="width: 100%"
        row-key="id"
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="product-expand-details">
              <!-- 商品详细信息 -->
              <div class="product-basic-info">
                <h4>商品详细信息</h4>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="info-item">
                      <label>商品ID:</label>
                      <span>{{ row.item_id }}</span>
                    </div>
                    <div class="info-item" v-if="row.category_path">
                      <label>分类路径:</label>
                      <span>{{ row.category_path }}</span>
                    </div>
                    <div class="info-item" v-if="row.shop_id">
                      <label>店铺ID:</label>
                      <span>{{ row.shop_id }}</span>
                    </div>
                    <div class="info-item" v-if="row.delivery_location">
                      <label>发货地:</label>
                      <span>{{ row.delivery_location }}</span>
                    </div>
                    <div class="info-item" v-if="row.has_sku !== null">
                      <label>是否有SKU:</label>
                      <el-tag :type="row.has_sku ? 'success' : 'info'" size="small">
                        {{ row.has_sku ? '是' : '否' }}
                      </el-tag>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item" v-if="row.code">
                      <label>API状态码:</label>
                      <el-tag :type="row.code === 200 ? 'success' : 'danger'" size="small">{{ row.code }}</el-tag>
                    </div>
                    <div class="info-item" v-if="row.stock !== null">
                      <label>总库存:</label>
                      <span :class="row.stock > 0 ? 'text-green-600' : 'text-red-600'">{{ row.stock }}</span>
                    </div>
                    <div class="info-item" v-if="row.category_id">
                      <label>类目ID:</label>
                      <span>{{ row.category_id }}</span>
                    </div>
                    <div class="info-item" v-if="row.item_type">
                      <label>商品类型:</label>
                      <el-tag size="small" type="info">{{ row.item_type }}</el-tag>
                    </div>
                    <div class="info-item" v-if="row.data_source">
                      <label>数据源:</label>
                      <el-tag size="small" type="primary">{{ row.data_source }}</el-tag>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 商品属性信息 -->
              <div class="product-props" v-if="row.props && (Array.isArray(row.props) ? row.props.length > 0 : Object.keys(row.props).length > 0)">
                <h4>商品属性</h4>
                <div class="props-grid">
                  <template v-if="Array.isArray(row.props)">
                    <div v-for="(prop, index) in row.props" :key="index" class="prop-item">
                      <span class="prop-name">{{ prop.prop_name || prop.name }}:</span>
                      <div class="prop-values">
                        <el-tag 
                          v-for="(value, vIndex) in (prop.values || [prop.value])" 
                          :key="vIndex" 
                          size="small" 
                          class="prop-value-tag"
                        >
                          {{ value.name || value }}
                        </el-tag>
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <div v-for="(value, key) in row.props" :key="key" class="prop-item">
                      <span class="prop-name">{{ key }}:</span>
                      <div class="prop-values">
                        <el-tag size="small" class="prop-value-tag">{{ value }}</el-tag>
                      </div>
                    </div>
                  </template>
                </div>
              </div>

              <!-- SKU详情 -->
              <div class="sku-details" v-if="row.skus && row.skus.length > 0">
                <h4>SKU 详情 ({{ row.skus.length }}个)</h4>
                <el-table :data="row.skus" stripe border size="small" max-height="300">
                  <el-table-column prop="sku_id" label="SKU ID" width="120" show-overflow-tooltip />
                  <el-table-column prop="name" label="SKU 名称" min-width="150" show-overflow-tooltip>
                    <template #default="{ row: sku }">
                      {{ sku.name || sku.sku_name || 'N/A' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="price" label="原价" width="100">
                    <template #default="{ row: sku }">
                      <span v-if="sku.price">¥{{ formatPrice(sku.price) }}</span>
                      <span v-else class="text-gray-400">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="sub_price" label="到手价" width="100">
                    <template #default="{ row: sku }">
                       <span v-if="sku.sub_price && sku.sub_price > 0">¥{{ formatPrice(sku.sub_price) }}</span>
                       <span v-else class="text-gray-400">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="official_guide_price" label="官方指导价" width="120">
                    <template #default="{ row: sku }">
                      <span v-if="sku.official_guide_price">¥{{ formatPrice(sku.official_guide_price) }}</span>
                      <span v-else class="text-gray-400">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="promotion_deviation_rate" label="促销价偏离率" width="120">
                    <template #default="{ row: sku }">
                      <span v-if="sku.promotion_deviation_rate !== null" 
                            :class="getDeviationRateClass(sku.promotion_deviation_rate)">
                        {{ sku.promotion_deviation_rate }}%
                      </span>
                      <span v-else class="text-gray-400">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="channel_price_deviation_rate" label="渠道价格偏离率" width="140">
                    <template #default="{ row: sku }">
                      <span v-if="sku.channel_price_deviation_rate !== null"
                            :class="getDeviationRateClass(sku.channel_price_deviation_rate)">
                        {{ sku.channel_price_deviation_rate }}%
                      </span>
                      <span v-else class="text-gray-400">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="quantity" label="库存" width="80">
                    <template #default="{ row: sku }">
                      <span :class="sku.quantity > 0 ? 'text-green-600' : 'text-red-600'">
                        {{ sku.quantity || 0 }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="image_url" label="图片" width="80">
                    <template #default="{ row: sku }">
                      <el-image
                        v-if="sku.image_url"
                        :src="sku.image_url"
                        :preview-src-list="[sku.image_url]"
                        fit="contain"
                        preview-teleported="true"
                        style="width: 50px; height: 50px"
                      />
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div v-else class="no-sku-details text-gray-500">
                <p>该商品没有SKU信息。</p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55" />
        <el-table-column prop="product_image" label="图片" width="80">
          <template #default="{ row }">
            <el-image
              v-if="row.product_image"
              :src="row.product_image"
              :preview-src-list="[row.product_image]"
              fit="contain"
              preview-teleported="true"
              style="width: 60px; height: 60px"
            />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="商品信息" min-width="250" show-overflow-tooltip>
          <template #default="{ row }">
            <el-link :href="row.url" type="primary" target="_blank" class="product-title-link">{{ row.title }}</el-link>
            <div class="product-sub-info">
              <span class="info-item">ID: {{ row.item_id }}</span>
              <span class="info-item" v-if="row.monitoring_task">
                  <el-tag size="small" type="success">{{ row.monitoring_task.name }}</el-tag>
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" width="120" sortable>
           <template #default="{ row }">
              <!-- {{ row }} --> <!-- 临时调试代码 -->
              <div v-if="row.price" class="price-info">
                <span class="current-price">¥{{ formatPrice(row.price) }}</span>
                <span v-if="row.lowest_price && row.lowest_price < row.price" class="lowest-price">
                  (最低 ¥{{ formatPrice(row.lowest_price) }})
                </span>
              </div>
               <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="min_hand_price" label="最低到手价" width="120" sortable>
          <template #default="{ row }">
            <span v-if="row.min_hand_price !== null && row.min_hand_price !== undefined">
              ¥{{ formatPrice(row.min_hand_price) }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="max_hand_price" label="最高到手价" width="120" sortable>
          <template #default="{ row }">
            <span v-if="row.max_hand_price !== null && row.max_hand_price !== undefined">
              ¥{{ formatPrice(row.max_hand_price) }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="总库存" width="110" sortable>
          <template #default="{ row }">
            <span v-if="row.stock !== null" :class="row.stock > 0 ? 'text-green-600 font-medium' : 'text-red-500 font-medium'">
                {{ row.stock }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="sales" label="销量" width="100" sortable>
            <template #default="{ row }">
                <span v-if="row.sales !== null">{{ row.sales }}</span>
                <span v-else class="text-gray-400">-</span>
            </template>
        </el-table-column>
        <el-table-column prop="last_collected_at" label="最后采集时间" width="160" sortable>
            <template #default="{ row }">
                <el-tooltip :content="row.last_collected_at" placement="top">
                    <span>{{ fromNow(row.last_collected_at) }}</span>
                </el-tooltip>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button text size="small" :icon="Document" @click="toggleRowDetail(row)">详情</el-button>
              <el-button text size="small" :icon="TrendCharts" @click="viewPriceHistory(row)">历史</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 产品对比对话框 -->
    <el-dialog
      v-model="compareDialogVisible"
      title="产品价格对比分析"
      width="95%"
      :close-on-click-modal="false"
    >
      <div class="compare-content" v-loading="compareLoading">
        <!-- 基本信息对比 -->
        <h4>基本信息对比</h4>
        <el-table :data="compareData" stripe style="width: 100%; margin-bottom: 24px;">
          <el-table-column prop="image" label="图片" width="80">
            <template #default="{ row }">
              <el-image
                v-if="row.image || row.main_image_url"
                :src="row.image || row.main_image_url"
                fit="cover"
                style="width: 50px; height: 50px; border-radius: 4px;"
              />
            </template>
          </el-table-column>
          <el-table-column prop="title" label="产品名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="data_source" label="数据源" width="120">
            <template #default="{ row }">
              <el-tag size="small" type="primary">{{ row.data_source }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="当前价格" width="120">
            <template #default="{ row }">
              <span :class="getPriceCompareClass(row.price)">
                ¥{{ formatPrice(row.price) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="min_hand_price" label="最低到手价" width="120">
            <template #default="{ row }">
              <span v-if="row.min_hand_price">¥{{ formatPrice(row.min_hand_price) }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="max_hand_price" label="最高到手价" width="120">
            <template #default="{ row }">
              <span v-if="row.max_hand_price">¥{{ formatPrice(row.max_hand_price) }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="库存" width="80" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="last_collected_at" label="更新时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.last_collected_at) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- SKU详细对比 -->
        <div v-for="(product, productIndex) in compareData" :key="product.id" class="product-sku-compare">
          <h4>{{ product.title }} - SKU详细对比</h4>
          <div v-if="product.skus && product.skus.length > 0">
            <el-table :data="product.skus" stripe size="small" style="width: 100%; margin-bottom: 20px;">
              <el-table-column prop="sku_id" label="SKU ID" width="120" show-overflow-tooltip />
              <el-table-column prop="name" label="SKU名称" min-width="150" show-overflow-tooltip />
              <el-table-column prop="price" label="原价" width="100">
                <template #default="{ row: sku }">
                  <span v-if="sku.price">¥{{ formatPrice(sku.price) }}</span>
                  <span v-else class="text-gray-400">-</span>
                </template>
              </el-table-column>
              <el-table-column prop="sub_price" label="到手价" width="100">
                <template #default="{ row: sku }">
                  <span v-if="sku.sub_price">¥{{ formatPrice(sku.sub_price) }}</span>
                  <span v-else class="text-gray-400">-</span>
                </template>
              </el-table-column>
              <el-table-column prop="official_guide_price" label="官方指导价" width="120">
                <template #default="{ row: sku }">
                  <span v-if="sku.official_guide_price">¥{{ formatPrice(sku.official_guide_price) }}</span>
                  <span v-else class="text-gray-400">-</span>
                </template>
              </el-table-column>
              <el-table-column prop="promotion_deviation_rate" label="促销价偏离率" width="120">
                <template #default="{ row: sku }">
                  <span v-if="sku.promotion_deviation_rate !== null" 
                        :class="getDeviationRateClass(sku.promotion_deviation_rate)">
                    {{ sku.promotion_deviation_rate }}%
                  </span>
                  <span v-else class="text-gray-400">-</span>
                </template>
              </el-table-column>
              <el-table-column prop="channel_price_deviation_rate" label="渠道价格偏离率" width="140">
                <template #default="{ row: sku }">
                  <span v-if="sku.channel_price_deviation_rate !== null"
                        :class="getDeviationRateClass(sku.channel_price_deviation_rate)">
                    {{ sku.channel_price_deviation_rate }}%
                  </span>
                  <span v-else class="text-gray-400">-</span>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="库存" width="80">
                <template #default="{ row: sku }">
                  <span :class="sku.quantity > 0 ? 'text-green-600' : 'text-red-600'">
                    {{ sku.quantity || 0 }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="no-sku-data">
            <p class="text-gray-500">该商品没有SKU信息</p>
          </div>
        </div>

        <!-- 对比总结 -->
        <div class="compare-summary" v-if="compareData.length > 0">
          <h4>对比总结</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-card">
                <div class="summary-title">价格范围</div>
                <div class="summary-content">
                  <div>最低：¥{{ getMinPrice() }}</div>
                  <div>最高：¥{{ getMaxPrice() }}</div>
                  <div>价差：¥{{ getPriceDifference() }}</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-card">
                <div class="summary-title">库存情况</div>
                <div class="summary-content">
                  <div>总SKU数：{{ getTotalSkuCount() }}</div>
                  <div>有库存SKU：{{ getInStockSkuCount() }}</div>
                  <div>缺货SKU：{{ getOutOfStockSkuCount() }}</div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-card">
                <div class="summary-title">偏离率统计</div>
                <div class="summary-content">
                  <div>平均促销偏离率：{{ getAveragePromotionDeviation() }}%</div>
                  <div>平均渠道偏离率：{{ getAverageChannelDeviation() }}%</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <template #footer>
        <el-button @click="compareDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="exportCompareData">导出对比数据</el-button>
      </template>
    </el-dialog>

    <!-- 产品详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="产品详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="product-detail" v-if="selectedProduct">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-image
              v-if="selectedProduct.image"
              :src="selectedProduct.image"
              fit="cover"
              style="width: 100%; max-height: 300px; border-radius: 8px;"
            />
            <div v-else class="no-image-large">
              <el-icon><Picture /></el-icon>
              <span>暂无图片</span>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="detail-info">
              <h3>{{ selectedProduct.title }}</h3>
              <div class="detail-row">
                <label>商品ID:</label>
                <span>{{ selectedProduct.item_id }}</span>
              </div>
              <div class="detail-row">
                <label>数据源:</label>
                <el-tag type="primary">{{ selectedProduct.data_source }}</el-tag>
              </div>
              <div class="detail-row">
                <label>当前价格:</label>
                <span class="price">¥{{ formatPrice(selectedProduct.price) }}</span>
                <span v-if="selectedProduct.original_price && selectedProduct.original_price > selectedProduct.price" class="original-price">
                  原价: ¥{{ formatPrice(selectedProduct.original_price) }}
                </span>
              </div>
              <div class="detail-row">
                <label>库存:</label>
                <span>{{ selectedProduct.quantity || 0 }}</span>
              </div>
              <div class="detail-row">
                <label>状态:</label>
                <el-tag :type="getStatusTagType(selectedProduct.status)">{{ selectedProduct.status }}</el-tag>
              </div>
              <div class="detail-row" v-if="selectedProduct.brand">
                <label>品牌:</label>
                <span>{{ selectedProduct.brand }}</span>
              </div>
              <div class="detail-row" v-if="selectedProduct.category">
                <label>分类:</label>
                <span>{{ selectedProduct.category }}</span>
              </div>
              <div class="detail-row">
                <label>更新时间:</label>
                <span>{{ formatDateTime(selectedProduct.last_collected_at) }}</span>
              </div>
              <div class="detail-row" v-if="selectedProduct.description">
                <label>描述:</label>
                <p class="description">{{ selectedProduct.description }}</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button v-if="selectedProduct?.url" type="primary" @click="openProductUrl(selectedProduct.url)">
          访问商品页面
        </el-button>
      </template>
    </el-dialog>

    <!-- 产品历史对话框 -->
    <el-dialog
      v-model="priceHistoryDialogVisible"
      title="产品历史版本"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="product-history" v-loading="priceHistoryLoading">
        <div v-if="productHistoryData && productHistoryData.length > 0">
          <el-table 
            :data="productHistoryData" 
            stripe 
            style="width: 100%"
            @row-click="viewHistoryDetail"
            row-class-name="history-row-hover"
          >
            <el-table-column prop="collected_at" label="采集时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.collected_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="price" label="价格" width="100">
              <template #default="{ row }">
                ¥{{ formatPrice(row.price) }}
              </template>
            </el-table-column>
            <el-table-column prop="original_price" label="原价" width="100">
              <template #default="{ row }">
                <span v-if="row.original_price">¥{{ formatPrice(row.original_price) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="stock" label="库存" width="80">
              <template #default="{ row }">
                {{ row.stock || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="sales" label="销量" width="80">
              <template #default="{ row }">
                {{ row.sales || 0 }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button text size="small" type="primary" @click.stop="viewHistoryDetail(row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination" style="margin-top: 20px; text-align: center;">
            <el-pagination
              v-model:current-page="historyPagination.current_page"
              v-model:page-size="historyPagination.per_page"
              :total="historyPagination.total"
              :page-sizes="[10, 20, 50]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleHistoryPageSizeChange"
              @current-change="handleHistoryCurrentChange"
            />
          </div>
        </div>
        <div v-else class="no-data">
          <el-empty description="暂无历史数据" />
        </div>
      </div>
      <template #footer>
        <el-button @click="priceHistoryDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElDialog } from 'element-plus'
import {
  Search,
  RefreshLeft,
  TrendCharts,
  Picture,
  View,
  Link,
  Document
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import analyticsApi from '@/api/analytics'
import { getAllDataSources } from '@/api/dataSource'
import { getAllMonitoringTasks } from '@/api/monitoringTask'
import productDataApi from '@/api/productData'
import { getDashboardStats } from '@/api/analytics'

const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const statsLoading = ref(true)
const compareLoading = ref(false)
const priceHistoryLoading = ref(false)

// 表格引用
const productTable = ref(null)

// 数据
const productData = ref([])
const dataSources = ref([])
const monitoringTasks = ref([])
const selectedProducts = ref([])
const dashboardStats = ref({
  taskGroups: 0,
  monitoringProducts: 0,
  runningTasks: 0,
  alerts: 0,
})

// 搜索表单
const searchForm = reactive({
  search: '',
  task_id: null,
  data_source_id: null,
  sort_by: 'last_collected_at',
  sort_order: 'desc',
})

const dateRange = ref([])

// 分页信息
const pagination = ref({
  current_page: 1,
  per_page: 10,
  total: 0
})

// 对话框状态
const compareDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const priceHistoryDialogVisible = ref(false)

// 对话框数据
const compareData = ref([])
const selectedProduct = ref(null)
const priceHistoryData = ref([])
const productHistoryData = ref([])

// 时间模式相关
const timeMode = ref('latest') // 'latest' 或 'specific'
const specificTime = ref(null)

// 历史数据分页
const historyPagination = ref({
  current_page: 1,
  per_page: 10,
  total: 0
})

// 当前查看历史的产品
const currentHistoryProduct = ref(null)

// 展开的原始数据
const expandedRawData = ref([])

// 方法
const fromNow = (dateTime) => {
  if (!dateTime) return '-';
  try {
    const date = new Date(dateTime);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);
    
    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 30) return `${diffDays}天前`;
    
    // 超过30天显示具体日期
    return date.toLocaleDateString('zh-CN');
  } catch (error) {
    console.error(`Error formatting date: ${dateTime}`, error);
    return dateTime; // fallback to original string
  }
};

async function loadProductData() {
  try {
    loading.value = true
    let params = {
      page: pagination.value.current_page,
      per_page: pagination.value.per_page,
      ...searchForm,
    }
    
    // 根据时间模式构建参数
    if (timeMode.value === 'specific' && specificTime.value) {
      // 指定时间点模式：获取历史数据
      if (searchForm.task_id) {
        const response = await productDataApi.getLatestHistoryForAllProducts(searchForm.task_id, {
          target_time: specificTime.value,
          page: pagination.value.current_page,
          per_page: pagination.value.per_page,
          search: searchForm.search,
          data_source_id: searchForm.data_source_id,
        })
        
        if (response && response.success) {
          productData.value = response.data?.data || []
          pagination.value = {
            current_page: response.data?.current_page || 1,
            per_page: response.data?.per_page || 10,
            total: response.data?.total || response.data?.meta?.total || 0
          }
          console.log('已加载历史产品数据:', productData.value.length, '条')
        } else {
          ElMessage.error(response?.message || '加载历史产品数据失败')
          productData.value = []
        }
        return
      } else {
        ElMessage.warning('请先选择监控任务')
        return
      }
    } else {
      // 最新数据模式
      params.date_from = dateRange.value?.[0] || ''
      params.date_to = dateRange.value?.[1] || ''
    }
    
    console.log('加载产品数据参数:', params)
    
    const response = await productDataApi.getProductData(params)
    
    console.log('产品数据API响应:', response)
    
    // 处理204 No Content状态码
    if (response.status === 204) {
      console.log('API返回204状态码，暂无产品数据')
      productData.value = []
      pagination.value = {
        current_page: 1,
        per_page: 10,
        total: 0
      }
      return
    }

    // 处理304 Not Modified状态码
    if (response.status === 304) {
      console.log('API返回304状态码，数据未修改')
      return
    }

    if (response && response.success) {
      productData.value = response.data?.data || []
      pagination.value = {
        current_page: response.data?.current_page || 1,
        per_page: response.data?.per_page || 10,
        total: response.data?.total || response.data?.meta?.total || 0
      }
      console.log('已加载产品数据:', productData.value.length, '条')
    } else if (response && response.data) {
      // 兼容直接返回数据的格式
      productData.value = response.data?.data || response.data || []
      pagination.value = {
        current_page: response.data?.current_page || 1,
        per_page: response.data?.per_page || 10,
        total: response.data?.total || response.data?.meta?.total || 0
      }
      console.log('已加载产品数据:', productData.value.length, '条')
    } else {
      console.warn('产品数据API返回格式异常:', response)
      productData.value = []
    }
  } catch (error) {
    console.error('加载产品数据错误:', error)

    // 根据错误状态码提供更具体的错误信息
    if (error.response?.status === 204) {
      console.log('服务器返回204，暂无产品数据')
      productData.value = []
    } else if (error.response?.status === 304) {
      console.log('数据未修改，保持当前产品数据')
    } else if (error.response?.status === 401) {
      console.error('未授权访问产品数据，请先登录')
      ElMessage.error('请先登录后再访问产品数据')
      productData.value = []
    } else {
      console.error('加载产品数据失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      ElMessage.error('加载产品数据失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      productData.value = []
    }
  } finally {
    loading.value = false
  }
}

async function loadStatistics() {
  try {
    statsLoading.value = true
    const response = await analyticsApi.getDashboardStats()
    
    console.log('Dashboard stats response:', response)
    
    if (response.success) {
      dashboardStats.value = response.data || {}
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  } finally {
    statsLoading.value = false
  }
}

async function loadDataSources() {
  try {
    console.log('开始加载数据源...')
    const response = await getAllDataSources()

    console.log('数据源API响应:', response)

    // 处理204 No Content状态码
    if (response.status === 204) {
      console.log('API返回204状态码，暂无数据源数据')
      dataSources.value = []
      return
    }

    // 处理304 Not Modified状态码
    if (response.status === 304) {
      console.log('API返回304状态码，数据未修改')
      return
    }

    if (response && response.success) {
      dataSources.value = response.data || []
      console.log('已加载数据源:', dataSources.value.length, '个')
    } else if (response && response.data) {
      // 兼容直接返回数据的格式
      dataSources.value = response.data || []
      console.log('已加载数据源:', dataSources.value.length, '个')
    } else {
      console.warn('数据源API返回格式异常:', response)
      dataSources.value = []
    }
  } catch (error) {
    console.error('加载数据源失败:', error)

    // 根据错误状态码提供更具体的错误信息
    if (error.response?.status === 204) {
      console.log('服务器返回204，暂无数据源数据')
      dataSources.value = []
    } else if (error.response?.status === 304) {
      console.log('数据未修改，保持当前数据源数据')
    } else if (error.response?.status === 401) {
      console.error('未授权访问数据源，请先登录')
      ElMessage.error('请先登录后再访问数据源')
      dataSources.value = []
    } else {
      console.error('加载数据源失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      ElMessage.error('加载数据源失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      dataSources.value = []
    }
  }
}

async function loadMonitoringTasks() {
  try {
    console.log('开始加载监控任务...')
    const response = await getAllMonitoringTasks()

    console.log('监控任务API响应:', response)

    // 处理204 No Content状态码
    if (response.status === 204) {
      console.log('API返回204状态码，暂无监控任务数据')
      monitoringTasks.value = []
      return
    }

    // 处理304 Not Modified状态码
    if (response.status === 304) {
      console.log('API返回304状态码，数据未修改')
      return
    }

    if (response && response.success) {
      monitoringTasks.value = response.data || []
      console.log('已加载监控任务:', monitoringTasks.value.length, '个')
    } else if (response && response.data) {
      // 兼容直接返回数据的格式
      monitoringTasks.value = response.data || []
      console.log('已加载监控任务:', monitoringTasks.value.length, '个')
    } else {
      console.warn('监控任务API返回格式异常:', response)
      monitoringTasks.value = []
    }
  } catch (error) {
    console.error('加载监控任务失败:', error)

    // 根据错误状态码提供更具体的错误信息
    if (error.response?.status === 204) {
      console.log('服务器返回204，暂无监控任务数据')
      monitoringTasks.value = []
    } else if (error.response?.status === 304) {
      console.log('数据未修改，保持当前监控任务数据')
    } else if (error.response?.status === 401) {
      console.error('未授权访问监控任务，请先登录')
      ElMessage.error('请先登录后再访问监控任务')
      monitoringTasks.value = []
    } else {
      console.error('加载监控任务失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      ElMessage.error('加载监控任务失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      monitoringTasks.value = []
    }
  }
}

async function refreshData() {
  await Promise.all([
    loadProductData(),
    loadStatistics(),
    loadDataSources(),
    loadMonitoringTasks()
  ])
}

// 事件处理方法
function handleSearch() {
  pagination.value.current_page = 1
  loadProductData()
  loadStatistics()
}

function handleDateRangeChange(value) {
  if (value && value.length === 2) {
    searchForm.date_from = value[0]
    searchForm.date_to = value[1]
  } else {
    searchForm.date_from = ''
    searchForm.date_to = ''
  }
  handleSearch()
}

function resetFilters() {
  searchForm.search = ''
  searchForm.task_id = null
  searchForm.data_source_id = null
  dateRange.value = []
  timeMode.value = 'latest'
  specificTime.value = null
  pagination.value.current_page = 1
  searchForm.sort_by = 'last_collected_at'
  searchForm.sort_order = 'desc'
  refreshData()
}

// 时间模式相关方法
function handleTimeModeChange(value) {
  if (value === 'latest') {
    specificTime.value = null
  }
  handleSearch()
}

function handleSpecificTimeChange(value) {
  handleSearch()
}

// 历史分页方法
function handleHistoryPageSizeChange(size) {
  historyPagination.value.per_page = size
  loadProductHistory()
}

function handleHistoryCurrentChange(page) {
  historyPagination.value.current_page = page
  loadProductHistory()
}

// 查看历史详情
function viewHistoryDetail(historyRow) {
  console.log('Navigating to history detail:', historyRow);
  const taskId = currentHistoryProduct.value.monitoring_task?.id;
  const itemId = currentHistoryProduct.value.item_id;
  
  if (!taskId || !itemId || !historyRow.id) {
    ElMessage.error('无法导航到历史详情页，缺少关键参数');
    return;
  }
  
  router.push({
    name: 'ProductHistoryDetail',
    params: { 
      taskId: taskId, 
      itemId: itemId, 
      historyId: historyRow.id 
    },
  });
}

function handleSelectionChange(selection) {
  selectedProducts.value = selection
}

function handleSortChange({ prop, order }) {
  if (prop) {
    searchForm.sort_by = prop
    searchForm.sort_order = order === 'ascending' ? 'asc' : 'desc'
    loadProductData()
  }
}

function handlePageSizeChange(size) {
  pagination.value.per_page = size
  loadProductData()
}

function handleCurrentChange(page) {
  pagination.value.current_page = page
  loadProductData()
}

// 产品操作方法
function viewProductDetail(product) {
  selectedProduct.value = product
  detailDialogVisible.value = true
}

async function viewPriceHistory(row) {
  console.log('Viewing history for product:', row);
  if (!row || !row.item_id || !row.monitoring_task?.id) {
    ElMessage.error('无法获取产品历史记录，缺少关键信息 (task_id, item_id)');
    return;
  }
  
  currentHistoryProduct.value = row;
  priceHistoryDialogVisible.value = true;
  priceHistoryLoading.value = true;
  historyPagination.value.current_page = 1; 
  
  loadProductHistory();
}

async function loadProductHistory() {
  if (!currentHistoryProduct.value) return;
  
  priceHistoryLoading.value = true;
  try {
    const taskId = currentHistoryProduct.value.monitoring_task?.id;
    const itemId = currentHistoryProduct.value.item_id;

    if (!taskId || !itemId) {
      ElMessage.error('无法加载历史记录，缺少 Task ID 或 Item ID');
      priceHistoryLoading.value = false;
      return;
    }

    const params = {
      page: historyPagination.value.current_page,
      per_page: historyPagination.value.per_page,
      sort_by: 'collected_at',
      sort_order: 'desc',
    };

    const response = await productDataApi.getProductHistory(taskId, itemId, params);
    
    if (response && response.success) {
      productHistoryData.value = response.data.data;
      historyPagination.value.total = response.data.total;
      historyPagination.value.current_page = response.data.current_page;
    } else {
      throw new Error(response.message || '获取产品历史数据失败');
    }
  } catch (error) {
    console.error('Error loading product history:', error);
    productHistoryData.value = [];
    ElMessage.error(error.message || '加载产品历史时发生错误');
  } finally {
    priceHistoryLoading.value = false;
  }
}

function openProductUrl(url) {
  window.open(url, '_blank')
}

const showCompareDialog = () => {
  ElMessage.info('功能开发中');
};

// 工具方法
function formatPrice(price) {
  if (price === null || price === undefined) return 'N/A';
  return Number(price).toFixed(2);
}

function formatNumber(num) {
  if (num === null || num === undefined) return '-';
  return num.toLocaleString();
}

function formatDateTime(dateTime) {
  return new Date(dateTime).toLocaleString('zh-CN')
}

function getQuantityTagType(quantity) {
  const qty = parseInt(quantity || 0)
  if (qty === 0) return 'danger'
  if (qty < 10) return 'warning'
  return 'success'
}

function getDeviationRateClass(rate) {
  if (rate === null || rate === undefined) return ''
  
  const rateNum = Number(rate)
  if (rateNum > 0) {
    // 正值：渠道降价，显示绿色
    return 'text-green-600 font-medium'
  } else if (rateNum < 0) {
    // 负值：渠道加价，显示红色
    return 'text-red-600 font-medium'
  } else {
    // 0值：无偏离，显示灰色
    return 'text-gray-600'
  }
}

function getStatusTagType(status) {
  if (status === 1 || status === true || status === '上架' || status === 'active') return 'success';
  if (status === 0 || status === false || status === '下架' || status === 'inactive') return 'danger';
  return 'info';
}

function getStatusText(status) {
  if (status === 1 || status === true || status === '上架' || status === 'active') return '上架';
  if (status === 0 || status === false || status === '下架' || status === 'inactive') return '下架';
  return '未知';
}

function getPriceCompareClass(price) {
  if (!compareData.value.length) return ''
  
  const prices = compareData.value.map(item => parseFloat(item.price || 0))
  const minPrice = Math.min(...prices)
  const maxPrice = Math.max(...prices)
  const currentPrice = parseFloat(price || 0)
  
  if (currentPrice === minPrice) return 'price-lowest'
  if (currentPrice === maxPrice) return 'price-highest'
  return ''
}

function toggleRawData(productId) {
  const index = expandedRawData.value.indexOf(productId)
  if (index > -1) {
    expandedRawData.value.splice(index, 1)
  } else {
    expandedRawData.value.push(productId)
  }
  
  // 找到对应的产品数据行
  const product = productData.value.find(p => p.id === productId)
  if (product && productTable.value) {
    // 使用Element Plus表格的toggleRowExpansion方法来切换行展开状态
    productTable.value.toggleRowExpansion(product)
  }
}

function toggleRowDetail(product) {
  toggleRawData(product.id)
}

// 对比功能相关方法
function getMinPrice() {
  if (!compareData.value || compareData.value.length === 0) return '0.00'
  
  let allPrices = []
  compareData.value.forEach(product => {
    if (product.price) allPrices.push(Number(product.price))
    if (product.skus) {
      product.skus.forEach(sku => {
        if (sku.sub_price) allPrices.push(Number(sku.sub_price))
        if (sku.price) allPrices.push(Number(sku.price))
      })
    }
  })
  
  return allPrices.length > 0 ? Math.min(...allPrices).toFixed(2) : '0.00'
}

function getMaxPrice() {
  if (!compareData.value || compareData.value.length === 0) return '0.00'
  
  let allPrices = []
  compareData.value.forEach(product => {
    if (product.price) allPrices.push(Number(product.price))
    if (product.skus) {
      product.skus.forEach(sku => {
        if (sku.sub_price) allPrices.push(Number(sku.sub_price))
        if (sku.price) allPrices.push(Number(sku.price))
      })
    }
  })
  
  return allPrices.length > 0 ? Math.max(...allPrices).toFixed(2) : '0.00'
}

function getPriceDifference() {
  const min = Number(getMinPrice())
  const max = Number(getMaxPrice())
  return (max - min).toFixed(2)
}

function getTotalSkuCount() {
  if (!compareData.value) return 0
  return compareData.value.reduce((total, product) => {
    return total + (product.skus ? product.skus.length : 0)
  }, 0)
}

function getInStockSkuCount() {
  if (!compareData.value) return 0
  return compareData.value.reduce((total, product) => {
    if (!product.skus) return total
    return total + product.skus.filter(sku => sku.quantity > 0).length
  }, 0)
}

function getOutOfStockSkuCount() {
  return getTotalSkuCount() - getInStockSkuCount()
}

function getAveragePromotionDeviation() {
  if (!compareData.value) return '0.00'
  
  let rates = []
  compareData.value.forEach(product => {
    if (product.skus) {
      product.skus.forEach(sku => {
        if (sku.promotion_deviation_rate !== null && sku.promotion_deviation_rate !== undefined) {
          rates.push(Number(sku.promotion_deviation_rate))
        }
      })
    }
  })
  
  if (rates.length === 0) return '0.00'
  const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length
  return average.toFixed(2)
}

function getAverageChannelDeviation() {
  if (!compareData.value) return '0.00'
  
  let rates = []
  compareData.value.forEach(product => {
    if (product.skus) {
      product.skus.forEach(sku => {
        if (sku.channel_price_deviation_rate !== null && sku.channel_price_deviation_rate !== undefined) {
          rates.push(Number(sku.channel_price_deviation_rate))
        }
      })
    }
  })
  
  if (rates.length === 0) return '0.00'
  const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length
  return average.toFixed(2)
}

function exportCompareData() {
  // 导出对比数据功能
  ElMessage.info('导出功能开发中...')
}

// 生命周期
onMounted(() => {
  console.log('渠道价格监测看板组件已挂载，开始加载数据...')
  refreshData()
})

// Helper function to format promotions for single-line display
const formatPromotionsForDisplay = (promotions) => {
  if (!promotions || promotions.length === 0) return '-';
  return promotions.map(p => `${p.content}: ${p.sub_content}`).join('; ');
};

// Helper function to format promotions for multi-line tooltip
const formatPromotionsForTooltip = (promotions) => {
  if (!promotions || promotions.length === 0) return '-';
  return promotions.map(p => `<div><strong>${p.content}:</strong> ${p.sub_content}</div>`).join('');
};
</script>

<style scoped>
.channel-price-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-stats {
  display: flex;
  gap: 16px;
}

.stat-card {
  text-align: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  min-width: 80px;
}

.stat-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
  color: white;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.filter-bar {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.data-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.no-image {
  width: 50px;
  height: 50px;
  border: 2px dashed #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.product-title {
  line-height: 1.4;
}

.product-meta {
  margin-top: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 价格信息样式 */
.price-info {
  line-height: 1.5;
}

.price-info .price-label {
  font-size: 12px;
  color: #6b7280;
  margin-right: 4px;
}

.price-info .current-price {
  font-weight: 600;
  color: #ef4444;
  font-size: 14px;
}

.price-info .sub-price {
  margin-bottom: 4px;
}

.price-info .original-price {
  margin-bottom: 2px;
}

.price-info .original-price-value {
  text-decoration: line-through;
  color: #9ca3af;
  font-size: 12px;
}

.price-info .price-range-value {
  font-size: 13px;
  color: #059669;
}

/* SKU信息样式 */
.sku-info {
  line-height: 1.5;
}

.sku-info .sku-name {
  margin-bottom: 4px;
}

.sku-info .label {
  font-size: 12px;
  color: #6b7280;
  margin-right: 4px;
}

/* 销售数据样式 */
.sales-data {
  line-height: 1.5;
}

.sales-data .label {
  font-size: 12px;
  color: #6b7280;
  margin-right: 4px;
}

.sales-data .value {
  font-weight: 600;
  color: #374151;
}

.sales-data .sales-count {
  margin-bottom: 4px;
}

/* 促销信息样式 */
.promotion-info {
  line-height: 1.5;
}

.promotion-tag {
  margin-bottom: 4px;
  margin-right: 4px;
  display: inline-block;
}

.promotion-detail {
  max-width: 200px;
}

.promotion-item {
  padding: 2px 0;
  font-size: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.promotion-item:last-child {
  border-bottom: none;
}

.text-gray-400 {
  color: #9ca3af;
  font-size: 12px;
}

.price-lowest {
  color: #10b981 !important;
  font-weight: bold;
}

.price-highest {
  color: #ef4444 !important;
  font-weight: bold;
}

/* 展开详情样式 */
.product-expand-details {
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
}

.product-expand-details h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.product-basic-info {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
}

.info-item label {
  font-weight: 600;
  color: #6b7280;
  min-width: 80px;
  margin-right: 12px;
}

.product-props {
  margin-bottom: 24px;
}

.props-grid {
  display: grid;
  gap: 12px;
}

.prop-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.prop-name {
  font-weight: 600;
  color: #374151;
  min-width: 80px;
}

.prop-values {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.prop-value-tag {
  margin: 0;
}

.sku-details {
  margin-bottom: 24px;
}

.raw-data-preview {
  margin-bottom: 0;
}

.raw-data-content {
  background: #1f2937;
  color: #f9fafb;
  border-radius: 6px;
  padding: 16px;
  margin-top: 12px;
  max-height: 400px;
  overflow: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.raw-data-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.no-sku-details {
  text-align: center;
  padding: 20px;
  color: #9ca3af;
}

.text-green-600 {
  color: #059669;
}

.text-red-600 {
  color: #dc2626;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .header-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-card {
    min-width: 60px;
    padding: 12px 16px;
  }
  
  .product-expand-details {
    padding: 16px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-item label {
    min-width: auto;
    margin-bottom: 4px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-stats {
    flex-direction: row;
    margin-top: 16px;
  }
  
  .channel-price-dashboard {
    padding: 12px;
  }
}

.sku-details {
  padding: 16px;
  background-color: #f9fafb;
}
.sku-details h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.promotion-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

/* 历史功能相关样式 */
.product-history .el-table__body-wrapper {
  max-height: 400px;
  overflow-y: auto;
}

.history-row-hover {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.history-row-hover:hover {
  background-color: #f5f7fa !important;
}

.product-history .pagination {
  padding: 20px 0;
  text-align: center;
  border-top: 1px solid #ebeef5;
}

/* 时间选择相关样式 */
.filter-bar .el-select {
  width: 100%;
}

.filter-bar .el-date-picker {
  width: 100%;
}

/* 历史按钮样式增强 */
.el-button-group .el-button:last-child {
  border-left: none;
}

.el-button-group .el-button + .el-button {
  margin-left: 0;
}

/* 对比功能相关样式 */
.compare-content h4 {
  margin: 20px 0 12px 0;
  font-size: 16px;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.compare-content h4:first-child {
  margin-top: 0;
}

.product-sku-compare {
  margin-bottom: 24px;
}

.no-sku-data {
  text-align: center;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  margin-bottom: 20px;
}

.compare-summary {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.summary-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.summary-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  font-size: 14px;
}

.summary-content {
  color: #6b7280;
  font-size: 13px;
  line-height: 1.6;
}

.summary-content > div {
  margin-bottom: 4px;
}

.summary-content > div:last-child {
  margin-bottom: 0;
}

/* 偏离率颜色样式 */
.text-gray-600 {
  color: #6b7280;
}

.font-medium {
  font-weight: 500;
}
</style> 