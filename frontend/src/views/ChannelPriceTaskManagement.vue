<template>
  <div class="channel-price-task-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">渠道价格监测</h1>
        <p class="page-description">管理价格监测任务</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="default" @click="showCreateTaskDialog" :loading="loading">
          <el-icon><Plus /></el-icon>
          <span class="btn-text">新增任务</span>
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-icon primary">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.taskGroups || 0 }}</div>
          <div class="stats-label">任务组</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon success">
          <el-icon><ShoppingBag /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.monitoringProducts || 0 }}</div>
          <div class="stats-label">监控商品</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon warning">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.runningTasks || 0 }}</div>
          <div class="stats-label">运行中</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon danger">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ statistics.alerts || 0 }}</div>
          <div class="stats-label">预警</div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <div class="section-header">
        <h2 class="section-title">任务列表</h2>
        <div class="section-actions">
          <el-input 
            v-model="searchKeyword"
            placeholder="搜索任务..." 
            class="search-input"
            prefix-icon="Search"
            @input="handleSearch"
            clearable
          />
          <el-button @click="refreshTasks" :loading="tasksLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <div class="task-cards" v-loading="tasksLoading">
        <div 
          class="task-card" 
          v-for="task in filteredTasks" 
          :key="task.id"
          @click="viewTaskDetails(task)"
        >
          <div class="task-header">
            <div :class="['task-status', task.status]"></div>
            <h3 class="task-name">{{ task.name }}</h3>
            <el-dropdown @command="(cmd) => handleTaskAction(cmd, task)" @click.stop="">
              <el-button size="small" link>
                <el-icon><Setting /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑任务</el-dropdown-item>
                  <el-dropdown-item command="run-now">立即执行</el-dropdown-item>
                  <el-dropdown-item command="start" v-if="task.status !== 'running'">启动任务</el-dropdown-item>
                  <el-dropdown-item command="pause" v-if="task.status === 'running'">暂停任务</el-dropdown-item>
                  <el-dropdown-item command="stop" v-if="task.status === 'running'">停止任务</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="task-content">
            <p class="task-desc">{{ task.description || '监控商品价格变动，及时发现异常情况' }}</p>
            <div class="task-meta">
              <span class="meta-item">
                <el-icon><Clock /></el-icon>
                {{ getFrequencyLabel(task.frequency_type) }}
              </span>
              <span class="meta-item">
                <el-icon><ShoppingBag /></el-icon>
                {{ task.target_products?.length || 0 }} 商品
              </span>
              <span class="meta-item">
                <el-icon><DataBoard /></el-icon>
                {{ task.data_source?.name || '未知' }}
              </span>
              <span class="meta-item" v-if="task.alert_rule_ids?.length">
                <el-icon><Warning /></el-icon>
                {{ task.alert_rule_ids.length }} 预警规则
              </span>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" v-if="!tasksLoading && filteredTasks.length === 0">
          <el-empty description="暂无监控任务">
            <el-button type="primary" @click="showCreateTaskDialog">创建第一个任务</el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog
      :title="isEditing ? '编辑监控任务' : '新增监控任务'"
      v-model="createTaskVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form :model="taskForm" :rules="taskRules" ref="taskFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据源" prop="data_source_id">
              <el-select v-model="taskForm.data_source_id" placeholder="请选择数据源" style="width: 100%">
                <el-option
                  v-for="ds in dataSources"
                  :key="ds.id"
                  :label="ds.name"
                  :value="ds.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="任务描述" prop="description">
          <el-input 
            v-model="taskForm.description" 
            type="textarea" 
            :rows="2"
            placeholder="请输入任务描述"
          />
        </el-form-item>

        <!-- 预警规则选择 -->
        <el-form-item label="预警规则" prop="alert_rule_ids">
          <div class="flex w-full items-center">
             <el-select 
              v-model="taskForm.alert_rule_ids" 
              multiple 
              placeholder="请选择预警规则" 
              class="flex-grow"
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="rule in alertRules"
                :key="rule.id"
                :label="`${rule.name} (${getRuleTypeLabel(rule.rule_type)})`"
                :value="rule.id"
              >
                <span style="float: left">{{ rule.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ getRuleTypeLabel(rule.rule_type) }}</span>
              </el-option>
            </el-select>
            <el-button @click="showNewRuleDialog" class="ml-2">快速创建规则</el-button>
          </div>
          <small style="color:#909399; width: 100%; display: block; margin-top: 4px;">
            选择适用于此任务的预警规则，或快速创建一个新规则
          </small>
        </el-form-item>

        <!-- 时间设置 -->
        <el-divider content-position="left">执行设置</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="执行频率" prop="frequency_type">
              <el-select v-model="taskForm.frequency_type" placeholder="请选择执行频率" style="width: 100%" @change="onFrequencyTypeChange">
                <el-option
                  v-for="(label, value) in frequencyTypes"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="taskForm.frequency_type === 'interval'" label="间隔时间(分钟)" prop="frequency_value">
              <el-input-number v-model="taskForm.frequency_value" :min="1" placeholder="请输入间隔分钟数" style="width: 100%" />
            </el-form-item>
            <el-form-item v-if="taskForm.frequency_type === 'cron'" label="Cron表达式" prop="cron_expression">
              <el-input v-model="taskForm.cron_expression" placeholder="例如: 0 * * * * (每小时)" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 定时执行设置 -->
        <div v-if="taskForm.frequency_type === 'scheduled'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="执行类型" prop="schedule_type">
                <el-select v-model="taskForm.schedule_type" placeholder="请选择执行类型" style="width: 100%" @change="onScheduleTypeChange">
                  <el-option label="每天执行" value="daily" />
                  <el-option label="指定日期执行" value="specific_date" />
                  <el-option label="指定周几执行" value="weekly" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
                          <el-form-item label="执行时间" prop="execution_time">
              <el-time-picker
                v-model="taskForm.execution_time"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择执行时间"
                style="width: 100%"
              />
              <small style="color:#909399;display:block;margin-top:4px;">
                设置每天的具体执行时间点
              </small>
            </el-form-item>
            </el-col>
          </el-row>
          
          <!-- 指定日期执行 -->
          <el-row :gutter="20" v-if="taskForm.schedule_type === 'specific_date'">
            <el-col :span="24">
              <el-form-item label="执行日期" prop="execution_dates">
                <el-date-picker
                  v-model="taskForm.execution_dates"
                  type="dates"
                  placeholder="选择执行日期（可多选）"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
                <small style="color:#909399;display:block;margin-top:4px;">
                  选择具体的日期，在所选日期的指定时间执行任务
                </small>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- 指定周几执行 -->
          <el-row :gutter="20" v-if="taskForm.schedule_type === 'weekly'">
            <el-col :span="24">
              <el-form-item label="执行星期" prop="execution_weekdays">
                <el-checkbox-group v-model="taskForm.execution_weekdays">
                  <el-checkbox label="1">周一</el-checkbox>
                  <el-checkbox label="2">周二</el-checkbox>
                  <el-checkbox label="3">周三</el-checkbox>
                  <el-checkbox label="4">周四</el-checkbox>
                  <el-checkbox label="5">周五</el-checkbox>
                  <el-checkbox label="6">周六</el-checkbox>
                  <el-checkbox label="0">周日</el-checkbox>
                </el-checkbox-group>
                <small style="color:#909399;display:block;margin-top:4px;">
                  选择一周中的哪几天执行，在选定日期的指定时间执行任务
                </small>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- 每天执行提示 -->
          <div v-if="taskForm.schedule_type === 'daily'" style="margin-top: 10px;">
            <small style="color:#909399;">
              每天在指定时间 {{ taskForm.execution_time || '00:00' }} 重复执行任务
            </small>
          </div>
        </div>

        <!-- 监控商品表格 -->
        <el-divider content-position="left">监控商品配置</el-divider>
        <el-form-item label="监控商品" prop="target_products">
          <div class="product-table-container">
            <div class="table-toolbar">
              <el-button type="primary" size="small" @click="addProductRow">
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
              <el-upload
                :show-file-list="false"
                accept=".xlsx,.xls"
                :before-upload="handleExcelUpload"
                style="display: inline-block; margin-left: 10px;"
              >
                <el-button size="small" type="success">
                  <el-icon><Upload /></el-icon>
                  Excel导入
                </el-button>
              </el-upload>
              <el-button size="small" @click="downloadTemplate">
                <el-icon><Download /></el-icon>
                下载模板
              </el-button>
            </div>
            
            <el-table 
              :data="filteredProducts" 
              style="width: 100%; margin-top: 10px"
              max-height="300"
              stripe
            >
              <el-table-column prop="product_id" label="商品ID" width="200">
                <template #default="{ row, $index }">
                  <el-input 
                    v-model="row.product_id" 
                    placeholder="请输入商品ID"
                    @blur="validateProductId(row, $index)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="official_guide_price" label="官方指导价" width="150">
                <template #default="{ row }">
                  <el-input-number 
                    v-model="row.official_guide_price" 
                    :precision="2"
                    :min="0"
                    placeholder="0.00"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注">
                <template #default="{ row }">
                  <el-input 
                    v-model="row.remark" 
                    placeholder="商品备注（可选）"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ $index }">
                  <el-button 
                    size="small" 
                    type="danger" 
                    link
                    @click="removeProductRow($index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="table-summary" v-if="taskForm.target_products.length">
              <div class="summary-info">
                <span>共 {{ taskForm.target_products.length }} 个商品</span>
                <span v-if="productSearchKeyword" style="margin-left: 15px; color: #909399;">
                  显示 {{ filteredProducts.length }} 个匹配结果
                </span>
              </div>
              <div class="summary-actions">
                <el-button size="small" type="warning" @click="clearAllProducts">
                  清空所有
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 商品搜索和管理 -->
        <el-form-item label="商品搜索">
          <div class="product-search-container">
            <el-input
              v-model="productSearchKeyword"
              placeholder="输入商品ID或备注进行搜索"
              @input="handleProductSearch"
              clearable
              style="width: 300px; margin-right: 10px;"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button @click="clearProductSearch" v-if="productSearchKeyword">
              清除筛选
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="自动启动">
          <el-switch v-model="taskForm.auto_start" />
          <small style="color:#909399;margin-left:8px;">创建后是否自动启动任务</small>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createTaskVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateTask" :loading="createLoading">
          {{ isEditing ? '更新任务' : '创建任务' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog
      title="任务详情"
      v-model="taskDetailVisible"
      width="900px"
    >
      <div v-if="selectedTask">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务名称">{{ selectedTask.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedTask.status)">
              {{ getStatusLabel(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数据源">{{ selectedTask.data_source?.name }}</el-descriptions-item>
          <el-descriptions-item label="执行频率">{{ getFrequencyLabel(selectedTask.frequency_type) }}</el-descriptions-item>
          <el-descriptions-item label="调度配置" v-if="selectedTask.frequency_type === 'scheduled'">{{ getScheduleInfo(selectedTask) }}</el-descriptions-item>
          <el-descriptions-item label="商品数量">{{ selectedTask.target_products?.length || 0 }}</el-descriptions-item>
          <el-descriptions-item label="预警规则">{{ selectedTask.alert_rule_ids?.length || 0 }} 个</el-descriptions-item>
          <el-descriptions-item label="执行次数">{{ selectedTask.run_count || 0 }}</el-descriptions-item>
          <el-descriptions-item label="成功率">{{ selectedTask.success_rate || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="上次运行">{{ formatDateTime(selectedTask.last_run_at) }}</el-descriptions-item>
          <el-descriptions-item label="下次运行">{{ formatDateTime(selectedTask.next_run_at) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ formatDateTime(selectedTask.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">{{ selectedTask.description || '暂无描述' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 商品列表详情 -->
        <el-divider content-position="left">监控商品列表</el-divider>
        <el-table :data="selectedTask.target_products" style="width: 100%" max-height="300">
          <el-table-column prop="product_id" label="商品ID" width="200" />
          <el-table-column prop="official_guide_price" label="官方指导价" width="120">
            <template #default="{ row }">
              <span v-if="row.official_guide_price">¥{{ row.official_guide_price }}</span>
              <span v-else style="color: #c0c4cc;">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注">
            <template #default="{ row }">
              <span v-if="row.remark">{{ row.remark }}</span>
              <span v-else style="color: #c0c4cc;">无备注</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 新建预警规则对话框 -->
    <el-dialog
      title="快速创建预警规则"
      v-model="newRuleDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="newAlertRule" :rules="newRuleRules" ref="newRuleFormRef" label-width="100px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="newAlertRule.name" placeholder="为新规则命名" />
        </el-form-item>
        <el-form-item label="规则类型" prop="rule_type">
          <el-select v-model="newAlertRule.rule_type" placeholder="请选择规则类型" style="width: 100%">
            <el-option
              v-for="(label, value) in ruleOptions.rule_types"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        
        <template v-if="needsOperatorAndThreshold(newAlertRule.rule_type)">
          <el-form-item label="操作符" prop="operator">
            <el-select v-model="newAlertRule.operator" placeholder="请选择操作符" style="width: 100%">
              <el-option
                v-for="(label, value) in ruleOptions.operators"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="阈值(%)" prop="threshold_values.value">
            <el-input-number 
              v-model="newAlertRule.threshold_values.value"
              :min="0"
              :max="100"
              placeholder="请输入0-100的百分比"
              style="width: 100%;"
            />
          </el-form-item>
        </template>
        
        <template v-else-if="newAlertRule.rule_type === 'listing_status_change'">
            <el-alert title="“上下架状态”预警说明" type="info" :closable="false" show-icon>
                此规则会在商品状态变为"下架"时触发预警，无需设置额外参数。
            </el-alert>
        </template>

        <el-form-item label="严重级别" prop="severity">
            <el-select v-model="newAlertRule.severity" placeholder="请选择严重级别" style="width: 100%">
                <el-option
                v-for="(label, value) in ruleOptions.severities"
                :key="value"
                :label="label"
                :value="value"
                />
            </el-select>
        </el-form-item>
        <el-form-item label="通知方式" prop="notification_method">
            <el-select v-model="newAlertRule.notification_method" multiple placeholder="请选择通知方式" style="width: 100%">
                <el-option
                v-for="(label, value) in ruleOptions.notification_methods"
                :key="value"
                :label="label"
                :value="value"
                />
            </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="newRuleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreateNewRule" :loading="newRuleLoading">
            创建并选用
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  FolderOpened, 
  ShoppingBag, 
  Timer, 
  Warning, 
  Setting,
  Clock,
  Refresh,
  DataBoard,
  Upload,
  Download,
  Search
} from '@element-plus/icons-vue'
import monitoringTaskApi from '../api/monitoringTask'
import dataSourceApi from '../api/dataSource'
import analyticsApi from '../api/analytics'
import apiClient from '../api/apiClient'
import * as XLSX from 'xlsx'

// 响应式数据
const loading = ref(false)
const tasksLoading = ref(false)
const createLoading = ref(false)
const createTaskVisible = ref(false)
const taskDetailVisible = ref(false)
const searchKeyword = ref('')
const productSearchKeyword = ref('')
const isEditing = ref(false)

// 数据
const monitoringTasks = ref([])
const dataSources = ref([])
const alertRules = ref([])
const ruleOptions = ref({
  rule_types: {},
  operators: {},
  severities: {},
  notification_methods: {},
  priorities: {}
})
const statistics = ref({
  taskGroups: 0,
  monitoringProducts: 0,
  runningTasks: 0,
  alerts: 0
})
const selectedTask = ref(null)

// 表单
const taskFormRef = ref()
const taskForm = ref({
  name: '',
  description: '',
  data_source_id: '',
  frequency_type: 'interval',
  frequency_value: 60,
  cron_expression: '',
  start_time: '',
  end_time: '',
  target_products: [],
  alert_rule_ids: [],
  auto_start: false,
  schedule_type: 'daily',
  execution_time: '',
  execution_dates: [],
  execution_weekdays: []
})

const taskRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  data_source_id: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  frequency_type: [
    { required: true, message: '请选择执行频率', trigger: 'change' }
  ],
  frequency_value: [
    { required: true, message: '请输入间隔时间', trigger: 'blur' },
    { type: 'number', min: 1, message: '间隔时间必须为正整数', trigger: 'blur' }
  ],
  cron_expression: [
    { required: true, message: '请输入 Cron 表达式', trigger: 'blur' }
  ],
  schedule_type: [
    { required: true, message: '请选择执行类型', trigger: 'change' }
  ],
  execution_time: [
    { required: true, message: '请选择执行时间', trigger: 'blur' }
  ],
  execution_dates: [
    { type: 'array', required: true, min: 1, message: '请选择至少一个执行日期', trigger: 'blur' }
  ],
  execution_weekdays: [
    { type: 'array', required: true, min: 1, message: '请选择至少一个执行星期', trigger: 'blur' }
  ],
  target_products: [
    { type: 'array', required: true, min: 1, message: '请添加至少一个商品', trigger: 'blur' }
  ]
}

// 频率类型
const frequencyTypes = ref<Record<string, string>>({
  'interval': '间隔执行',
  'cron': 'Cron表达式',
  'scheduled': '定时执行'
})

// 计算属性
const filteredTasks = computed(() => {
  if (!searchKeyword.value) return monitoringTasks.value
  return monitoringTasks.value.filter(task => 
    task.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (task.description && task.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
})

// 方法
const fetchTasks = async () => {
  try {
    tasksLoading.value = true
    const response = await monitoringTaskApi.getAllTasks()
    monitoringTasks.value = response.data || []
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    tasksLoading.value = false
  }
}

const fetchDataSources = async () => {
  try {
    const response = await dataSourceApi.getAll()
    dataSources.value = response.data || []
  } catch (error) {
    console.error('获取数据源失败:', error)
  }
}

const fetchAlertRules = async () => {
  try {
    console.log('开始获取预警规则...')
    const response = await apiClient.get('/alert-rules', {
      params: {
        status: 1,
        per_page: 100
      }
    })

    console.log('预警规则API完整响应:', response)
    console.log('预警规则API响应数据:', response.data)

    // 处理204 No Content状态码
    if (response.status === 204) {
      console.log('API返回204状态码，暂无预警规则数据')
      alertRules.value = []
      ElMessage.info('暂无可用的预警规则，请先在预警中心创建规则')
      return
    }

    // 处理304 Not Modified状态码
    if (response.status === 304) {
      console.log('API返回304状态码，数据未修改')
      return
    }

    // 处理AlertRuleController::index返回的分页格式
    if (response.data && response.data.success) {
      let rules = []

      // 后端返回格式: { success: true, data: $paginatedRules, message: '' }
      // $paginatedRules 是 Laravel 分页对象，包含 data 和其他分页信息
      if (response.data.data && response.data.data.data && Array.isArray(response.data.data.data)) {
        // 分页格式：{ success: true, data: { data: [...], total: 10, current_page: 1, ... } }
        rules = response.data.data.data
      } else if (response.data.data && Array.isArray(response.data.data)) {
        // 兼容直接数组格式（如果后端有时返回直接数组）
        rules = response.data.data
      } else {
        console.warn('预警规则数据格式异常:', response.data.data)
        rules = []
      }

      alertRules.value = rules
      console.log('解析后的预警规则数量:', rules.length)
      console.log('解析后的预警规则:', rules)

      if (rules.length === 0) {
        console.warn('未获取到任何预警规则，可能需要先创建预警规则')
        ElMessage.info('暂无可用的预警规则，请先在预警中心创建规则')
      }
    } else {
      console.error('预警规则API返回失败:', response.data?.message || '未知错误')
      ElMessage.error('获取预警规则失败: ' + (response.data?.message || '未知错误'))
      alertRules.value = []
    }
  } catch (error) {
    console.error('获取预警规则失败，详细错误:', error)

    // 根据错误状态码提供更具体的错误信息
    if (error.response?.status === 204) {
      console.log('服务器返回204，暂无预警规则数据')
      alertRules.value = []
      ElMessage.info('暂无可用的预警规则，请先在预警中心创建规则')
    } else if (error.response?.status === 304) {
      console.log('数据未修改，保持当前预警规则数据')
    } else if (error.response) {
      console.error('错误响应状态:', error.response.status)
      console.error('错误响应数据:', error.response.data)

      if (error.response.status === 401) {
        ElMessage.error('未授权访问，请重新登录')
      } else if (error.response.status === 403) {
        ElMessage.error('权限不足，无法访问预警规则')
      } else if (error.response.status === 500) {
        ElMessage.error('服务器内部错误，请联系管理员')
      } else {
        ElMessage.error('获取预警规则失败: ' + (error.response.data?.message || '请检查网络连接'))
      }
      alertRules.value = []
    } else {
      ElMessage.error('网络连接失败，请检查网络设置')
      alertRules.value = []
    }
  }
}

const fetchStatistics = async () => {
  try {
    const response = await analyticsApi.getDashboardStatistics()
    statistics.value = response.data || {}
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const showCreateTaskDialog = () => {
  isEditing.value = false
  createTaskVisible.value = true
  resetTaskForm()
}

const resetTaskForm = () => {
  taskForm.value = {
    name: '',
    description: '',
    data_source_id: '',
    frequency_type: 'interval',
    frequency_value: 60,
    cron_expression: '',
    start_time: '',
    end_time: '',
    target_products: [],
    alert_rule_ids: [],
    auto_start: false,
    schedule_type: 'daily',
    execution_time: '',
    execution_dates: [],
    execution_weekdays: []
  }
}

// 商品行操作
const addProductRow = () => {
  taskForm.value.target_products.push({
    product_id: '',
    official_guide_price: null,
    remark: ''
  })
}

const removeProductRow = (index) => {
  taskForm.value.target_products.splice(index, 1)
}

const clearAllProducts = () => {
  ElMessageBox.confirm('确定要清空所有商品吗？', '确认操作', {
    type: 'warning'
  }).then(() => {
    taskForm.value.target_products = []
    ElMessage.success('已清空所有商品')
  })
}

const validateProductId = (row, index) => {
  if (!row.product_id.trim()) {
    ElMessage.warning(`第 ${index + 1} 行商品ID不能为空`)
    return false
  }
  
  // 检查重复
  const duplicateIndex = taskForm.value.target_products.findIndex((item, idx) => 
    idx !== index && item.product_id === row.product_id
  )
  
  if (duplicateIndex !== -1) {
    ElMessage.warning(`商品ID "${row.product_id}" 已存在`)
    return false
  }
  
  return true
}

// 商品搜索和过滤
const filteredProducts = computed(() => {
  if (!productSearchKeyword.value) {
    return taskForm.value.target_products
  }
  
  const keyword = productSearchKeyword.value.toLowerCase()
  return taskForm.value.target_products.filter(product => 
    product.product_id.toLowerCase().includes(keyword) ||
    (product.remark && product.remark.toLowerCase().includes(keyword))
  )
})

const handleProductSearch = () => {
  // 实现搜索逻辑，这里使用计算属性自动过滤
  if (productSearchKeyword.value && filteredProducts.value.length === 0) {
    ElMessage.info('未找到匹配的商品')
  }
}

const clearProductSearch = () => {
  productSearchKeyword.value = ''
}

// Excel处理
const handleExcelUpload = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target!.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })
      const firstSheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData: any[] = XLSX.utils.sheet_to_json(firstSheet, { header: 1 })
      
      // 跳过标题行，解析数据
      const products = []
      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i]
        if (row && row[0]) {
          products.push({
            product_id: String(row[0]).trim(),
            official_guide_price: row[1] ? Number(row[1]) : null,
            remark: row[2] ? String(row[2]).trim() : ''
          })
        }
      }
      
      if (products.length > 0) {
        // 合并到现有商品，去重
        const existingIds = new Set(taskForm.value.target_products.map(p => p.product_id))
        const newProducts = products.filter(p => !existingIds.has(p.product_id))
        
        taskForm.value.target_products.push(...newProducts)
        ElMessage.success(`成功导入 ${newProducts.length} 个商品`)
      } else {
        ElMessage.warning('Excel文件中没有找到有效的商品数据')
      }
    } catch (error) {
      console.error('Excel解析失败:', error)
      ElMessage.error('Excel文件解析失败，请检查文件格式')
    }
  }
  reader.readAsArrayBuffer(file)
  return false // 阻止上传
}

const downloadTemplate = () => {
  const templateData = [
    ['商品ID', '官方指导价', '备注'],
    ['PRODUCT001', 99.99, '示例商品1'],
    ['PRODUCT002', 199.99, '示例商品2']
  ]
  
  const worksheet = XLSX.utils.aoa_to_sheet(templateData)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, '商品导入模板')
  XLSX.writeFile(workbook, '商品导入模板.xlsx')
}

const handleCreateTask = async () => {
  try {
    // 动态验证规则
    const dynamicRules = { ...taskRules }
    
    // 根据频率类型调整验证规则
    if (taskForm.value.frequency_type === 'interval') {
      dynamicRules.frequency_value[0].required = true
    } else if (taskForm.value.frequency_type === 'cron') {
      dynamicRules.cron_expression[0].required = true
    } else if (taskForm.value.frequency_type === 'scheduled') {
      dynamicRules.schedule_type[0].required = true
      dynamicRules.execution_time[0].required = true
      
      if (taskForm.value.schedule_type === 'specific_date') {
        dynamicRules.execution_dates[0].required = true
      } else if (taskForm.value.schedule_type === 'weekly') {
        dynamicRules.execution_weekdays[0].required = true
      }
    }
    
    await taskFormRef.value.validate()
    
    // 验证商品数据
    if (taskForm.value.target_products.length === 0) {
      ElMessage.error('请至少添加一个监控商品')
      return
    }
    
    for (let i = 0; i < taskForm.value.target_products.length; i++) {
      const product = taskForm.value.target_products[i]
      if (!product.product_id.trim()) {
        ElMessage.error(`第 ${i + 1} 行商品ID不能为空`)
        return
      }
    }
    
    createLoading.value = true
    
    if (isEditing.value && taskForm.value.id) {
      await monitoringTaskApi.updateTask(taskForm.value.id, { ...taskForm.value })
      ElMessage.success('任务更新成功')
    } else {
      await monitoringTaskApi.createTask({ ...taskForm.value })
      ElMessage.success('任务创建成功')
    }
    
    createTaskVisible.value = false
    await fetchTasks()
    await fetchStatistics()
  } catch (error) {
    console.error('操作任务失败:', error)
    ElMessage.error('操作失败')
  } finally {
    createLoading.value = false
  }
}

const viewTaskDetails = (task) => {
  selectedTask.value = task
  taskDetailVisible.value = true
}

const handleTaskAction = async (command, task) => {
  switch (command) {
    case 'edit':
      showEditTaskDialog(task)
      break
    case 'start':
      try {
        await monitoringTaskApi.updateTaskStatus(task.id, 'running')
        ElMessage.success('任务已启动')
        fetchTasks()
      } catch (error) {
        ElMessage.error('启动任务失败')
      }
      break
    case 'pause':
      try {
        await monitoringTaskApi.updateTaskStatus(task.id, 'paused')
        ElMessage.success('任务已暂停')
        fetchTasks()
      } catch (error) {
        ElMessage.error('暂停任务失败')
      }
      break
    case 'stop':
      try {
        await monitoringTaskApi.updateTaskStatus(task.id, 'stopped')
        ElMessage.success('任务已停止')
        fetchTasks()
      } catch (error) {
        ElMessage.error('停止任务失败')
      }
      break
    case 'run-now':
      try {
        await monitoringTaskApi.runTaskNow(task.id)
        ElMessage.success('已提交执行')
      } catch (error) {
        ElMessage.error('执行失败')
      }
      break
    case 'delete':
      ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
        type: 'warning'
      }).then(async () => {
        try {
          await monitoringTaskApi.deleteTask(task.id)
          ElMessage.success('任务已删除')
          fetchTasks()
          fetchStatistics()
        } catch (error) {
          ElMessage.error('删除任务失败')
        }
      })
      break
  }
}

const refreshTasks = () => {
  fetchTasks()
  fetchStatistics()
}

const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
}

// 编辑任务对话框
const showEditTaskDialog = (task) => {
  isEditing.value = true
  createTaskVisible.value = true
  taskForm.value = {
    id: task.id,
    name: task.name,
    description: task.description || '',
    data_source_id: task.data_source_id,
    frequency_type: task.frequency_type,
    frequency_value: task.frequency_value,
    cron_expression: task.cron_expression || '',
    start_time: task.start_time || '',
    end_time: task.end_time || '',
    target_products: Array.isArray(task.target_products) ? [...task.target_products] : [],
    alert_rule_ids: Array.isArray(task.alert_rule_ids) ? [...task.alert_rule_ids] : [],
    auto_start: task.auto_start,
    schedule_type: task.schedule_type || 'daily',
    execution_time: task.execution_time || '',
    execution_dates: Array.isArray(task.execution_dates) ? [...task.execution_dates] : [],
    execution_weekdays: Array.isArray(task.execution_weekdays) ? [...task.execution_weekdays] : []
  }
}

// 辅助方法
const getFrequencyLabel = (type) => {
  return frequencyTypes.value[type] || type
}

const getStatusLabel = (status) => {
  const labels = {
    pending: '待运行',
    running: '运行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败'
  }
  return labels[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    pending: '',
    running: 'success',
    paused: 'warning',
    completed: 'info',
    failed: 'danger'
  }
  return types[status] || ''
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString()
}

const getScheduleInfo = (task) => {
  if (!task.schedule_type) return '-'
  
  const time = task.execution_time || '00:00'
  
  switch (task.schedule_type) {
    case 'daily':
      return `每天 ${time} 执行`
    case 'specific_date':
      if (task.execution_dates && task.execution_dates.length > 0) {
        return `指定日期 ${task.execution_dates.join(', ')} 的 ${time} 执行`
      }
      return `指定日期 ${time} 执行`
    case 'weekly':
      if (task.execution_weekdays && task.execution_weekdays.length > 0) {
        const weekdays = task.execution_weekdays.map(day => {
          const names = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
          return names[parseInt(day)]
        }).join(', ')
        return `每周 ${weekdays} 的 ${time} 执行`
      }
      return `每周指定日期 ${time} 执行`
    default:
      return '-'
  }
}

const onFrequencyTypeChange = () => {
  // 根据频率类型变化重置相关字段
  if (taskForm.value.frequency_type !== 'scheduled') {
    taskForm.value.schedule_type = 'daily'
    taskForm.value.execution_time = ''
    taskForm.value.execution_dates = []
    taskForm.value.execution_weekdays = []
  }
}

const onScheduleTypeChange = () => {
  // 根据执行类型变化重置相关字段
  taskForm.value.execution_dates = []
  taskForm.value.execution_weekdays = []
}

// 辅助方法
const getRuleTypeLabel = (ruleTypes) => {
  const ruleTypeOptions = {
    'listing_status_change': '上下架状态',
    'promotion_price_deviation': '促销价偏离率',
    'channel_price_deviation': '渠道价格偏离率',
  }
  
  if (Array.isArray(ruleTypes)) {
    return ruleTypes.map(type => ruleTypeOptions[type] || type).join(', ')
  }
  return ruleTypeOptions[ruleTypes] || ruleTypes
}

// 快速创建规则相关
const showNewRuleDialog = () => {
  // 这里可以打开一个简化的规则创建对话框，或者跳转到规则管理页面
  ElMessageBox.confirm('将跳转到预警规则管理页面创建新规则', '创建预警规则', {
    confirmButtonText: '前往创建',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // 跳转到规则创建页面
    window.open('/channel-price/alerts', '_blank')
  })
}

// 生命周期
onMounted(async () => {
  try {
    loading.value = true
    await Promise.all([
      fetchTasks(),
      fetchDataSources(),
      fetchAlertRules(),
      fetchStatistics()
    ])
  } catch (error) {
    console.error('初始化数据加载失败:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.channel-price-task-management {
  padding: 16px;
  background: #f8f9fa;
  min-height: 100%;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.page-description {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stats-icon.primary {
  background: linear-gradient(135deg, #409EFF, #66b3ff);
}

.stats-icon.success {
  background: linear-gradient(135deg, #67C23A, #85ce61);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #ebb563);
}

.stats-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #f78989);
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

.stats-label {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 4px;
}

.task-list-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.search-input {
  width: 250px;
}

.task-cards {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.task-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.task-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.task-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.task-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.task-status.running {
  background: #67C23A;
}

.task-name {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
  margin: 0;
}

.task-desc {
  font-size: 14px;
  color: #606266;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

/* 新增商品表格相关样式 */
.product-table-container {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
}

.table-toolbar {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-summary {
  background: #f8f9fa;
  padding: 8px 16px;
  border-top: 1px solid #dcdfe6;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.summary-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.summary-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-search-container {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

/* 新增任务对话框样式优化 */
.el-dialog {
  border-radius: 12px;
  overflow: hidden;

  .el-dialog__header {
    background: linear-gradient(135deg, #409eff, #66b1ff);
    color: white;
    padding: 20px 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 18px;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background-color: #fafbfc;
  }
}

/* 表单样式优化 */
.el-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      line-height: 1.5;
    }

    .el-form-item__content {
      .el-input, .el-select, .el-textarea {
        .el-input__wrapper, .el-select__wrapper, .el-textarea__inner {
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #c0c4cc;
          }

          &:focus, &.is-focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
          }
        }
      }

      .el-input-number {
        width: 100%;

        .el-input__wrapper {
          border-radius: 8px;
        }
      }

      .el-time-picker {
        width: 100%;

        .el-input__wrapper {
          border-radius: 8px;
        }
      }
    }
  }

  .el-divider {
    margin: 24px 0;

    .el-divider__text {
      font-weight: 500;
      color: #409eff;
      background-color: #fafbfc;
      padding: 0 16px;
    }
  }
}

/* 预警规则选择区域样式 */
.el-form-item[prop="alert_rule_ids"] {
  .el-form-item__content {
    .flex {
      display: flex;
      align-items: flex-start;
      gap: 12px;

      .flex-grow {
        flex: 1;
      }

      .ml-2 {
        margin-left: 0;
        white-space: nowrap;
        border-radius: 8px;
        background: linear-gradient(135deg, #67c23a, #85ce61);
        border-color: #67c23a;
        color: white;

        &:hover {
          background: linear-gradient(135deg, #5daf34, #7bc862);
          border-color: #5daf34;
        }
      }
    }

    small {
      display: block;
      margin-top: 8px;
      padding: 8px 12px;
      background-color: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 6px;
      color: #409eff;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

/* 执行设置区域样式 */
.el-row {
  .el-col {
    .el-form-item {
      .el-form-item__content {
        small {
          display: block;
          margin-top: 6px;
          color: #909399;
          font-size: 12px;
          line-height: 1.3;
        }
      }
    }
  }
}

/* 商品表格样式优化 */
.product-table-container {
  margin-top: 16px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;

  .el-table {
    .el-table__header {
      th {
        background-color: #f8f9fa;
        color: #606266;
        font-weight: 500;
        border-bottom: 1px solid #e4e7ed;
      }
    }

    .el-table__body {
      tr:hover {
        background-color: #f5f7fa;
      }

      td {
        border-bottom: 1px solid #f0f0f0;
      }
    }
  }
}

/* 对话框底部按钮样式 */
.el-dialog__footer {
  padding: 16px 24px;
  background-color: #f8f9fa;
  border-top: 1px solid #e4e7ed;

  .el-button {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;

    &.el-button--primary {
      background: linear-gradient(135deg, #409eff, #66b1ff);
      border-color: #409eff;

      &:hover {
        background: linear-gradient(135deg, #3a8ee6, #5ca7f7);
        border-color: #3a8ee6;
      }
    }

    &.el-button--default {
      &:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
    }
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .channel-price-task-management {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .btn-text {
    display: none;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stats-card {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .stats-number {
    font-size: 20px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 16px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .task-cards {
    grid-template-columns: 1fr;
    padding: 16px;
    gap: 12px;
  }
  
  .task-card {
    padding: 12px;
  }
  
  .table-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .table-toolbar > * {
    align-self: flex-start;
  }

  /* 移动端对话框优化 */
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;

    .el-dialog__header {
      padding: 16px 20px;

      .el-dialog__title {
        font-size: 16px;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }
  }

  .el-form {
    .el-row {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .el-form-item {
      margin-bottom: 16px;

      .el-form-item__label {
        font-size: 14px;
      }
    }
  }

  /* 预警规则选择在移动端的优化 */
  .el-form-item[prop="alert_rule_ids"] {
    .el-form-item__content {
      .flex {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;

        .ml-2 {
          margin-left: 0;
          align-self: flex-end;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .channel-price-task-management {
    padding: 8px;
  }
  
  .page-header {
    padding: 12px;
  }
  
  .page-title {
    font-size: 18px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  /* 超小屏幕对话框优化 */
  .el-dialog {
    width: 98% !important;
    margin: 2vh auto !important;

    .el-dialog__header {
      padding: 12px 16px;

      .el-dialog__title {
        font-size: 15px;
      }
    }

    .el-dialog__body {
      padding: 16px;
    }
  }

  .el-form {
    .el-form-item {
      margin-bottom: 12px;

      .el-form-item__label {
        font-size: 13px;
        margin-bottom: 4px;
      }
    }

    .el-divider {
      margin: 16px 0;

      .el-divider__text {
        font-size: 13px;
        padding: 0 12px;
      }
    }
  }
}
</style> 